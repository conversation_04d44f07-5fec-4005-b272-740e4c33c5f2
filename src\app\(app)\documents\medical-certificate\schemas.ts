import * as z from "zod";

export const certificateSchema = z.object({
  patientSearch: z.string().optional(),
  patientName: z.string().optional(),
  idCard: z.string().optional(),
  clinicHistoryNumber: z.string().optional(),
  age: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  attentionNumber: z.string().optional(),
  attentionDateNumeric: z.string().optional(),
  attentionDateWritten: z.string().optional(),
  attendedAt: z.string().optional(),
  diagnosis: z.string().min(1, "El diagnóstico es requerido."),
  procedures: z.string().optional(),
  observations: z.string().optional(),
  workplace: z.string().optional(),
  workActivity: z.string().optional(),
  contingencyType: z.string().optional(),
  hasSymptoms: z.enum(["Sí", "No"]).optional(),
});
