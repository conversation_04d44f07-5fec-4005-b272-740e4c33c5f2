"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth/auth";

export async function deletePatient(patientId: number) {
  try {
    // Verificar autenticación
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return {
        success: false,
        message: "No autenticado. Por favor, inicie sesión.",
      };
    }

    const user = verifyToken(token);
    if (typeof user !== 'object' || user === null || !(user as any).userId) {
      return {
        success: false,
        message: "Token inválido. Por favor, inicie sesión nuevamente.",
      };
    }

    // Verificar que el paciente existe
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      include: {
        evaluations: true,
        appointments: true,
        medicalRecords: true,
        parents: true,
      },
    });

    if (!patient) {
      return {
        success: false,
        message: "Paciente no encontrado.",
      };
    }

    // Información sobre lo que se va a eliminar
    const evaluationsCount = patient.evaluations.length;
    const appointmentsCount = patient.appointments.length;
    const parentsCount = patient.parents.length;
    const medicalRecordsCount = patient.medicalRecords.length;

    // Eliminar en orden: primero las relaciones, luego el paciente
    await prisma.$transaction(async (tx) => {
      // Eliminar exámenes de laboratorio de evaluaciones (si los hubiera)
      await tx.evaluationLabExam.deleteMany({
        where: {
          evaluation: {
            patientId: patientId
          }
        }
      });

      // Eliminar evaluaciones
      await tx.evaluation.deleteMany({
        where: { patientId: patientId }
      });

      // Eliminar exámenes de laboratorio de registros médicos
      await tx.laboratoryExam.deleteMany({
        where: {
          medicalRecord: {
            patientId: patientId
          }
        }
      });

      // Eliminar registros médicos
      await tx.medicalRecord.deleteMany({
        where: { patientId: patientId }
      });

      // Eliminar citas
      await tx.appointment.deleteMany({
        where: { patientId: patientId }
      });

      // Eliminar padres
      await tx.parent.deleteMany({
        where: { patientId: patientId }
      });

      // Finalmente, eliminar el paciente
      await tx.patient.delete({
        where: { id: patientId }
      });
    });

    // Revalidar las rutas relevantes
    revalidatePath("/patients");

    console.log(`Patient deleted with ID: ${patientId} (${patient.name})`);

    // Construir mensaje detallado
    let deletionDetails = [];
    if (evaluationsCount > 0) deletionDetails.push(`${evaluationsCount} evaluación(es)`);
    if (appointmentsCount > 0) deletionDetails.push(`${appointmentsCount} cita(s)`);
    if (parentsCount > 0) deletionDetails.push(`${parentsCount} padre(s)/tutor(es)`);
    if (medicalRecordsCount > 0) deletionDetails.push(`${medicalRecordsCount} registro(s) médico(s)`);

    const detailsMessage = deletionDetails.length > 0
      ? ` Se eliminaron también: ${deletionDetails.join(', ')}.`
      : '';

    return {
      success: true,
      message: `Paciente "${patient.name}" eliminado exitosamente.${detailsMessage}`,
    };
  } catch (error) {
    console.error("Error deleting patient:", error);
    
    return {
      success: false,
      message: "Error interno del servidor. Por favor, intente nuevamente.",
    };
  }
}
