"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "@/hooks/use-toast";
import { FilePlus2, Loader2 } from "lucide-react";
import ExamenLaboratorio from "@/components/ExamenLaboratorio";
import { saveEvaluationForPatient } from "./actions";

// Esquema simplificado, sin los exámenes manuales
const newEvaluationSchema = z.object({
  consultationReason: z.string().min(1, "El motivo de consulta es requerido."),
  currentIllness: z.string().min(1, "La descripción de la enfermedad es requerida."),
  bowelHabits: z.string().optional(),
  weight: z.string().optional(),
  height: z.string().optional(),
  headCircumference: z.string().optional(),
  bloodPressure: z.string().optional(),
  temperature: z.string().optional(),
  cardiacFrequency: z.string().optional(),
  oxygenSaturation: z.string().optional(),
  generalObservations: z.string().optional(),
  systemsReview: z.string().optional(),
  physicalExam: z.string().optional(),
  analysis: z.string().optional(),
  paraclinical: z.string().optional(),
  diagnosticImpression: z.string().optional(),
  actionPlan: z.string().optional(),
});

type EvaluationFormValues = z.infer<typeof newEvaluationSchema>;

type UploadedFile = {
    file?: File;
    category: string;
};

interface NewEvaluationFormProps {
  patient: any;
  onEvaluationSaved: () => void;
}

export function NewEvaluationForm({ patient, onEvaluationSaved }: NewEvaluationFormProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<EvaluationFormValues>({
    resolver: zodResolver(newEvaluationSchema),
    defaultValues: {
      consultationReason: "",
      currentIllness: "",
      bowelHabits: "",
      weight: "",
      height: "",
      headCircumference: "",
      bloodPressure: "",
      temperature: "",
      cardiacFrequency: "",
      oxygenSaturation: "",
      generalObservations: "",
      systemsReview: "",
      physicalExam: "",
      analysis: "",
      paraclinical: "",
      diagnosticImpression: "",
      actionPlan: "",
    },
  });

  // Estado para la subida de archivos
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [fileCategory, setFileCategory] = React.useState('');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleFileUpload = () => {
    if (selectedFile && fileCategory) {
      setUploadedFiles([
        ...uploadedFiles,
        { file: selectedFile, category: fileCategory },
      ]);
      setSelectedFile(null);
    }
  };

  const handleFileRemove = (index: number) => {
    setUploadedFiles(uploadedFiles.filter((_, i) => i !== index));
  };

  async function onSubmit(values: EvaluationFormValues) {
    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("jsonData", JSON.stringify(values));
    
    uploadedFiles.forEach(f => {
        if (f.file) {
            formData.append("files", f.file);
            formData.append("fileCategories", f.category);
        }
    });

    try {
      const result = await saveEvaluationForPatient(patient.id, formData);
      
      if (result.success) {
        toast({
          title: "Evaluación Guardada",
          description: "La nueva evaluación se ha registrado exitosamente.",
        });
        form.reset();
        setUploadedFiles([]);
        onEvaluationSaved();
      } else {
        toast({
          variant: "destructive",
          title: "Error al guardar",
          description: result.message || "No se pudo guardar la evaluación.",
        });
      }
    } catch (error) {
      console.error("Error saving evaluation:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Ocurrió un error al guardar la evaluación.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FilePlus2 className="h-5 w-5 text-primary" />
          Evaluación Subsecuente para {patient.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* ... (todos los FormField existentes) ... */}
            <FormField control={form.control} name="consultationReason" render={({ field }) => (<FormItem><FormLabel>Motivo de Consulta *</FormLabel><FormControl><Textarea placeholder="Describa el motivo principal de la consulta..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            <FormField control={form.control} name="currentIllness" render={({ field }) => (<FormItem><FormLabel>Subjetivo *</FormLabel><FormControl><Textarea placeholder="Describa los síntomas y hallazgos subjetivos..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <FormField control={form.control} name="weight" render={({ field }) => (<FormItem><FormLabel>Peso (kg)</FormLabel><FormControl><Input placeholder="25.5" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField control={form.control} name="height" render={({ field }) => (<FormItem><FormLabel>Talla (cm)</FormLabel><FormControl><Input placeholder="120" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField control={form.control} name="temperature" render={({ field }) => (<FormItem><FormLabel>Temperatura (°C)</FormLabel><FormControl><Input placeholder="36.5" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField control={form.control} name="bloodPressure" render={({ field }) => (<FormItem><FormLabel>Presión Arterial</FormLabel><FormControl><Input placeholder="110/70" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField control={form.control} name="cardiacFrequency" render={({ field }) => (<FormItem><FormLabel>Frecuencia Cardíaca</FormLabel><FormControl><Input placeholder="90" {...field} /></FormControl><FormMessage /></FormItem>)} />
              <FormField control={form.control} name="oxygenSaturation" render={({ field }) => (<FormItem><FormLabel>Saturación O2 (%)</FormLabel><FormControl><Input placeholder="98" {...field} /></FormControl><FormMessage /></FormItem>)} />
            </div>
            <FormField control={form.control} name="physicalExam" render={({ field }) => (<FormItem><FormLabel>Objetivo</FormLabel><FormControl><Textarea placeholder="Hallazgos objetivos del examen físico..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            <FormField control={form.control} name="analysis" render={({ field }) => (<FormItem><FormLabel>Análisis</FormLabel><FormControl><Textarea placeholder="Análisis de los hallazgos..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            <FormField control={form.control} name="diagnosticImpression" render={({ field }) => (<FormItem><FormLabel>Impresión Diagnóstica</FormLabel><FormControl><Textarea placeholder="Diagnóstico presuntivo..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            <FormField control={form.control} name="actionPlan" render={({ field }) => (<FormItem><FormLabel>Plan</FormLabel><FormControl><Textarea placeholder="Tratamiento y seguimiento..." {...field} /></FormControl><FormMessage /></FormItem>)} />
            
            <ExamenLaboratorio
                uploadedFiles={uploadedFiles}
                onFileSelect={handleFileSelect}
                onFileUpload={handleFileUpload}
                onFileRemove={handleFileRemove}
                selectedFile={selectedFile}
                fileCategory={fileCategory}
                setFileCategory={setFileCategory}
            />

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Guardando...
                </>
              ) : (
                "Guardar Evaluación"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
