// src/app/api/auth/login/route.ts
import { NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { comparePasswords, generateToken } from '@/lib/auth/auth'
import { cookies } from 'next/headers'

export async function POST(req: Request) {
  try {
    const { email, password } = await req.json()

    if (!email || !password) {
      return NextResponse.json({ error: 'Faltan campos' }, { status: 400 })
    }

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        alias: true,
        role: true,
        active: true,
        specialty: true,
        msp: true,
        phone: true,
        createdAt: true,
        password: true
      } as any,
    }) as any;

    // Protección contra timing attacks
    if (!user || !user.password) {
      await comparePasswords(password, '$2b$10$invalidsaltinvalidsaltinv.uq9Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1Q1')
      return NextResponse.json({ error: 'Usuario no encontrado' }, { status: 401 })
    }

    // Verifica si el usuario está activo (si tienes ese campo)
    if (user.active === false) {
      return NextResponse.json({ error: 'Usuario inactivo' }, { status: 403 })
    }

    const valid = await comparePasswords(password, user.password)
    if (!valid) {
      return NextResponse.json({ error: 'Contraseña incorrecta' }, { status: 401 })
    }

    const token = generateToken({
      userId: user.id,
      alias: user.alias,
      role: user.role,
      active: user.active,
    })

    const cookieStore = await cookies();
    cookieStore.set('token', token, {
      httpOnly: true,
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 días
    })

    return NextResponse.json({
      message: 'Login exitoso',
      user: {
        id: user.id,
        email: user.email,
        alias: user.alias,
        role: user.role,
      },
    })
  } catch (error) {
    return NextResponse.json({ error: 'Error interno' }, { status: 500 })
  }
}