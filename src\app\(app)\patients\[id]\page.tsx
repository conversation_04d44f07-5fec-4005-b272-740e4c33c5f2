import { PatientRecordView } from "./patient-record-view";
import prisma from "@/lib/prisma";
import { notFound } from "next/navigation";

async function getPatientWithHistory(id: string) {
  try {
    const patient = await prisma.patient.findUnique({
      where: { id: parseInt(id) },
      include: {
        medicalRecords: true,
        parents: true,
        appointments: {
          orderBy: {
            date: 'desc'
          }
        },
        evaluations: {
          include: {
            doctor: {
              select: {
                name: true,
                specialty: true
              }
            },
            evaluationLabExams: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!patient) {
      return null;
    }

    return patient;
  } catch (error) {
    console.error("Error fetching patient:", error);
    return null;
  }
}

interface PatientPageProps {
  params: {
    id: string;
  };
}

export default async function PatientPage({ params }: PatientPageProps) {
  const { id } = await params;
  const patient = await getPatientWithHistory(id);

  if (!patient) {
    notFound();
  }

  return (
    <div className="h-full">
      <PatientRecordView patient={patient} />
    </div>
  );
}
