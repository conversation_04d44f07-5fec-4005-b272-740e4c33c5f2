import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import crypto from 'crypto';
import { sendPasswordResetEmail } from '@/lib/email';

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: 'El correo electrónico es requerido' }, { status: 400 });
    }

    // Buscar el usuario por email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        alias: true,
        active: true,
      },
    });

    // Por seguridad, siempre devolvemos el mismo mensaje, exista o no el usuario
    if (!user || !user.active) {
      return NextResponse.json({ 
        message: 'Si el correo electrónico existe en nuestro sistema, recibirá un enlace de recuperación.' 
      });
    }

    // Generar token de recuperación
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hora

    // Guardar el token en la base de datos
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    });

    // Enviar email de recuperación
    try {
      await sendPasswordResetEmail(user.email, user.name || user.alias, resetToken);
    } catch (emailError) {
      console.error('Error enviando email:', emailError);
      // No revelamos el error del email por seguridad
      return NextResponse.json({
        message: 'Si el correo electrónico existe en nuestro sistema, recibirá un enlace de recuperación.'
      });
    }

    // En desarrollo, devolver el enlace directamente para facilitar las pruebas
    if (process.env.NODE_ENV === 'development') {
      const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:9003'}/reset-password?token=${resetToken}`;
      return NextResponse.json({
        message: 'Si el correo electrónico existe en nuestro sistema, recibirá un enlace de recuperación.',
        resetUrl: resetUrl // Solo en desarrollo
      });
    }

    return NextResponse.json({
      message: 'Si el correo electrónico existe en nuestro sistema, recibirá un enlace de recuperación.'
    });

  } catch (error) {
    console.error('Error en forgot-password:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}
