import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { hashPassword, verifyToken } from '@/lib/auth/auth'

export async function POST(req: NextRequest) {
  try {
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json({ error: 'No autenticado' }, { status: 401 })
    }

    const userData = verifyToken(token)

    if (!userData || typeof userData === 'string' || !('userId' in userData) || typeof userData.userId !== 'number') {
      return NextResponse.json({ error: 'Token inválido o usuario no encontrado' }, { status: 401 })
    }

    const { password } = await req.json()

    if (!password || password.length < 8) {
      return NextResponse.json({ error: 'Contraseña muy corta' }, { status: 400 })
    }

    const hashed = await hashPassword(password)
    await prisma.user.update({
      where: { id: userData.userId },
      data: { password: hashed } as any,
    })

    return NextResponse.json({ message: 'Contraseña actualizada' })
  } catch (err) {
    return NextResponse.json({ error: 'Error interno' }, { status: 500 })
  }
}