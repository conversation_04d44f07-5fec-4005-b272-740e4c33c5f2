
export type Patient = {
  id: string;
  recordNumber: string;
  name: string;
  idNumber: string;
  dob: string;
  gender: "Masculino" | "Femenino";
  insurance: string;
};

export const mockPatients: Patient[] = [
  {
    id: "1",
    recordNumber: "P001",
    name: "<PERSON>",
    idNumber: "123456789",
    dob: "2018-05-10",
    gender: "Femenino",
    insurance: "Seguro ABC",
  },
  {
    id: "2",
    recordNumber: "P002",
    name: "<PERSON>",
    idNumber: "987654321",
    dob: "2020-02-20",
    gender: "<PERSON><PERSON><PERSON><PERSON>",
    insurance: "Salud Total",
  },
  {
    id: "3",
    recordNumber: "P003",
    name: "<PERSON><PERSON><PERSON>",
    idNumber: "456789123",
    dob: "2019-11-30",
    gender: "Femenino",
    insurance: "Mi Salud",
  },
  {
    id: "4",
    recordNumber: "P004",
    name: "<PERSON>",
    idNumber: "321654987",
    dob: "2021-08-15",
    gender: "Ma<PERSON><PERSON><PERSON>",
    insurance: "Seguro ABC",
  },
  {
    id: "5",
    recordNumber: "P005",
    name: "<PERSON><PERSON>",
    idNumber: "654987321",
    dob: "2017-01-25",
    gender: "Femenino",
    insurance: "Salud Total",
  },
];

export type User = {
  id: string;
  name: string;
  email: string;
  role: string;
  specialty: string;
  msp: string;
  phone: string;
};

export const mockUsers: User[] = [
  {
    id: "USRf532cd",
    name: "Maria del Cisne Arguello Bermeo",
    email: "<EMAIL>",
    role: "Médico",
    specialty: "Gastroenteróloga",
    msp: "MSP-1258",
    phone: "0983882267",
  },
  {
    id: "USRce258b",
    name: "Admin",
    email: "<EMAIL>",
    role: "Administrador",
    specialty: "-",
    msp: "-",
    phone: "-",
  },
];
