"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { evaluationSchema } from "./schemas";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth/auth";
import fs from "fs/promises";
import path from "path";

export async function saveEvaluation(data: z.infer<typeof evaluationSchema>) {
  try {
    // Verificar autenticación
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return {
        success: false,
        message: "No autenticado. Por favor, inicie sesión.",
      };
    }

    const user = verifyToken(token);
    if (typeof user !== 'object' || user === null || !(user as any).userId) {
      return {
        success: false,
        message: "Token inválido. Por favor, inicie sesión nuevamente.",
      };
    }

    // Validar los datos
    const validatedData = evaluationSchema.parse(data);

    // Generar alias único para el paciente basado en nombre y fecha
    const patientAlias = `PAT_${validatedData.patient.name.replace(/\s+/g, '_').toUpperCase()}_${Date.now()}`;

    // Buscar si el paciente ya existe por nombre y fecha de nacimiento
    let existingPatient = await prisma.patient.findFirst({
      where: {
        name: validatedData.patient.name,
      },
    });

    let patient;
    
    if (existingPatient) {
      // Actualizar paciente existente
      patient = await prisma.patient.update({
        where: { id: existingPatient.id },
        data: {
          name: validatedData.patient.name,
          idCard: validatedData.patient.idCard,
          gender: validatedData.patient.gender,
          dob: validatedData.patient.dob,
          age: validatedData.patient.age,
        },
      });
    } else {
      // Crear nuevo paciente
      patient = await prisma.patient.create({
        data: {
          alias: patientAlias,
          name: validatedData.patient.name,
          idCard: validatedData.patient.idCard,
          gender: validatedData.patient.gender,
          dob: validatedData.patient.dob,
          age: validatedData.patient.age,
        },
      });
    }

    // Crear o actualizar el registro médico
    let medicalRecord = await prisma.medicalRecord.findFirst({
      where: {
        record_number: validatedData.appointment.recordNumber,
        patientId: patient.id,
      },
    });

    if (!medicalRecord) {
      medicalRecord = await prisma.medicalRecord.create({
        data: {
          record_number: validatedData.appointment.recordNumber,
          patientId: patient.id,
        },
      });
    }

    // Crear o actualizar información de los padres
    let father = await prisma.parent.findFirst({
      where: {
        name: validatedData.father.name,
        patientId: patient.id,
        type: "father",
      },
    });

    if (father) {
      await prisma.parent.update({
        where: { id: father.id },
        data: {
          name: validatedData.father.name,
          age: validatedData.father.age || null,
          address: validatedData.father.address || null,
          phone: validatedData.father.phone || null,
          occupation: validatedData.father.occupation || null,
        },
      });
    } else if (validatedData.father.name) {
      await prisma.parent.create({
        data: {
          name: validatedData.father.name,
          age: validatedData.father.age || null,
          address: validatedData.father.address || null,
          phone: validatedData.father.phone || null,
          occupation: validatedData.father.occupation || null,
          type: "father",
          patientId: patient.id,
        },
      });
    }

    let mother = await prisma.parent.findFirst({
      where: {
        name: validatedData.mother.name,
        patientId: patient.id,
        type: "mother",
      },
    });

    if (mother) {
      await prisma.parent.update({
        where: { id: mother.id },
        data: {
          name: validatedData.mother.name,
          age: validatedData.mother.age || null,
          address: validatedData.mother.address || null,
          phone: validatedData.mother.phone || null,
          occupation: validatedData.mother.occupation || null,
        },
      });
    } else if (validatedData.mother.name) {
      await prisma.parent.create({
        data: {
          name: validatedData.mother.name,
          age: validatedData.mother.age || null,
          address: validatedData.mother.address || null,
          phone: validatedData.mother.phone || null,
          occupation: validatedData.mother.occupation || null,
          type: "mother",
          patientId: patient.id,
        },
      });
    }

    // Crear la cita
    const appointment = await prisma.appointment.create({
      data: {
        date: validatedData.appointment.date,
        time: validatedData.appointment.time,
        recordNumber: validatedData.appointment.recordNumber,
        insurance: validatedData.appointment.insurance,
        pediatrician: validatedData.appointment.pediatrician,
        referrer: validatedData.appointment.referrer,
        patientId: patient.id,
      },
    });

    // Generar alias único para la evaluación
    const evaluationAlias = `EVAL_${patient.alias}_${Date.now()}`;

    // Crear la evaluación
    const evaluation = await prisma.evaluation.create({
      data: {
        alias: evaluationAlias,
        doctorId: (user as any).userId,
        patientId: patient.id,
        consultationReason: validatedData.medical.consultationReason,
        currentIllness: validatedData.medical.currentIllness,
        upperDigestiveSymptoms: validatedData.medical.upperDigestiveSymptoms,
        lowerDigestiveSymptoms: validatedData.medical.lowerDigestiveSymptoms,
        bowelHabits: validatedData.medical.bowelHabits,
        weight: validatedData.medical.weight,
        height: validatedData.medical.height,
        headCircumference: validatedData.medical.headCircumference,
        bloodPressure: validatedData.medical.bloodPressure,
        temperature: validatedData.medical.temperature,
        cardiacFrequency: validatedData.medical.cardiacFrequency,
        oxygenSaturation: validatedData.medical.oxygenSaturation,
        perinatalHistory: validatedData.medical.perinatalHistory,
        nutritionalHistory: validatedData.medical.nutritionalHistory,
        developmentHistory: validatedData.medical.developmentHistory,
        immunizations: validatedData.medical.immunizations,
        personalMedicalHistory: validatedData.medical.personalMedicalHistory,
        familyMedicalHistory: validatedData.medical.familyMedicalHistory,
        systemsReview: validatedData.medical.systemsReview,
        physicalExam: validatedData.medical.physicalExam,
        analysis: validatedData.medical.analysis,
        paraclinical: validatedData.medical.paraclinical,
        diagnosticImpression: validatedData.medical.diagnosticImpression,
        actionPlan: validatedData.medical.actionPlan,
      },
    });

    // Procesar archivos de laboratorio si existen
    if (validatedData.labFiles && validatedData.labFiles.length > 0) {
      const uploadsDir = path.join(process.cwd(), "public", "uploads");
      await fs.mkdir(uploadsDir, { recursive: true });

      for (const labFile of validatedData.labFiles) {
        if (labFile.file && typeof labFile.file.name === 'string' && typeof (labFile.file as any).arrayBuffer === 'function') {
          const labExamAlias = `LAB_${evaluation.alias}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const fileExtension = path.extname(labFile.file.name);
          const fileName = `${labExamAlias}${fileExtension}`;
          const filePath = path.join(uploadsDir, fileName);
          const publicPath = `/uploads/${fileName}`;

          const buffer = Buffer.from(await (labFile.file as any).arrayBuffer());
          await fs.writeFile(filePath, buffer);

          await prisma.evaluationLabExam.create({
            data: {
              alias: labExamAlias,
              category: labFile.category,
              fileName: labFile.file.name,
              filePath: publicPath,
              evaluationId: evaluation.id,
            },
          });
        }
      }
    }

    // Revalidar las rutas relevantes
    revalidatePath("/patients");
    revalidatePath("/evaluations");

    console.log(`Evaluation saved with ID: ${evaluation.id} for patient: ${patient.name}`);

    return {
      success: true,
      message: `Evaluación para "${validatedData.patient.name}" guardada exitosamente.`,
      evaluationId: evaluation.id,
      patientId: patient.id,
    };
  } catch (error) {
    console.error("Error saving evaluation:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Datos de evaluación inválidos. Por favor, revise los campos requeridos.",
      };
    }

    return {
      success: false,
      message: "Error interno del servidor. Por favor, intente nuevamente.",
    };
  }
}