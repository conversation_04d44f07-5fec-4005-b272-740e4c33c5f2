"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TestTube, Upload, FileText, X } from "lucide-react";
import { Label } from "@/components/ui/label";
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";

type UploadedFile = {
    file?: File;
    category: string;
};

interface ExamenLaboratorioProps {
    uploadedFiles: UploadedFile[];
    onFileSelect: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onFileUpload: () => void;
    onFileRemove: (index: number) => void;
    selectedFile: File | null;
    fileCategory: string;
    setFileCategory: (value: string) => void;
}

export const ExamenLaboratorio: React.FC<ExamenLaboratorioProps> = ({
    uploadedFiles,
    onFileSelect,
    onFileUpload,
    onFileRemove,
    selectedFile,
    fileCategory,
    setFileCategory,
}) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <TestTube className="text-primary" />
                    Exámenes de Laboratorio
                </CardTitle>
                <CardDescription>
                    Subir y gestionar resultados de exámenes de laboratorio (PDF) y estudios de imagen (imágenes).
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-6">
                    <div className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg border">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-[2fr_3fr_1fr] gap-4 items-end">
                            <div className="space-y-1.5">
                                <Label htmlFor="fileCategorySelect">Categoría de Archivo</Label>
                                <Select onValueChange={setFileCategory} value={fileCategory}>
                                    <SelectTrigger id="fileCategorySelect">
                                        <SelectValue placeholder="Seleccionar categoría" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Examen de Laboratorio (PDF)">
                                            Examen de Laboratorio (PDF)
                                        </SelectItem>
                                        <SelectItem value="Estudio de Imagen (Imágenes)">
                                            Estudio de Imagen (Imágenes)
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-1.5">
                                <Label htmlFor="labFileInput">Seleccionar Archivo</Label>
                                <div className="flex h-10 w-full items-center rounded-md border border-input bg-background">
                                    <label htmlFor="labFileInput" className="cursor-pointer flex items-center px-3 h-full rounded-l-md border-r">
                                        <span className="text-sm">Seleccionar...</span>
                                        <Input
                                            id="labFileInput"
                                            type="file"
                                            className="sr-only"
                                            onChange={onFileSelect}
                                            accept=".pdf,.jpg,.jpeg,.png"
                                        />
                                    </label>
                                    <p className="text-sm text-muted-foreground flex-1 truncate px-3">
                                        {selectedFile?.name ?? "Ningún archivo seleccionado"}
                                    </p>
                                </div>
                            </div>
                            <Button
                                type="button"
                                onClick={onFileUpload}
                                disabled={!selectedFile || !fileCategory}
                                className="w-full"
                            >
                                <Upload className="mr-2 h-4 w-4" />
                                Subir Archivo
                            </Button>
                        </div>
                    </div>
                    <div className="space-y-2">
                        {uploadedFiles.length === 0 ? (
                            <div className="text-center text-muted-foreground py-10 border-2 border-dashed rounded-md">
                                <p>Aún no se han subido archivos.</p>
                            </div>
                        ) : (
                            <ul className="divide-y rounded-md border">
                                {uploadedFiles.map((item, index) => (
                                    <li key={index} className="flex items-center justify-between p-3">
                                        <div className="flex items-center gap-3">
                                            <FileText className="h-6 w-6 text-muted-foreground" />
                                            <div>
                                                <p className="font-medium text-sm">
                                                    {item.file ? item.file.name : "Archivo existente"}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {item.category}
                                                    {item.file && ` - ${(item.file.size / 1024).toFixed(2)} KB`}
                                                </p>
                                            </div>
                                        </div>
                                        <Button variant="ghost" size="icon" onClick={() => onFileRemove(index)}>
                                            <X className="h-4 w-4 text-red-500" />
                                            <span className="sr-only">Remove file</span>
                                        </Button>
                                    </li>
                                ))}
                            </ul>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ExamenLaboratorio;