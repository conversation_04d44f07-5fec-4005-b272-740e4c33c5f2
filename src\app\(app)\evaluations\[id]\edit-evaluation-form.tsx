"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import Link from "next/link";
import { evaluationSchema } from "../new/schemas";
import { updateEvaluation } from "./actions";

// Importar componentes del formulario
import { EvaluationForm } from "../new/evaluation-form";

interface EditEvaluationFormProps {
  evaluationId: number;
  initialData: z.infer<typeof evaluationSchema>;
}

export function EditEvaluationForm({ evaluationId, initialData }: EditEvaluationFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<z.infer<typeof evaluationSchema>>({
    resolver: zodResolver(evaluationSchema),
    defaultValues: initialData,
  });

  async function onSubmit(values: z.infer<typeof evaluationSchema>) {
    console.log("EditEvaluationForm onSubmit called with values:", values);
    console.log("evaluationId:", evaluationId);
    setIsSubmitting(true);

    try {
      console.log("Calling updateEvaluation...");
      const result = await updateEvaluation(evaluationId, values);
      
      if (result.success) {
        toast({
          title: "Evaluación Actualizada",
          description: "Los cambios se han guardado exitosamente.",
        });
        router.push("/patients");
      } else {
        toast({
          variant: "destructive",
          title: "Error al actualizar",
          description: result.message || "No se pudo actualizar la evaluación.",
        });
      }
    } catch (error) {
      console.error("Error updating evaluation:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Ocurrió un error al actualizar la evaluación.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="space-y-6">
      {/* Header con navegación */}
      <div className="flex items-center justify-between">
        <Link href="/patients">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Pacientes
          </Button>
        </Link>
        <div className="flex gap-2">
          <Button
            type="submit"
            form="edit-evaluation-form"
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Guardando...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Actualizar
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Formulario */}
      <div className="space-y-6">
        <EvaluationForm
          initialData={initialData}
          isEditing={true}
          evaluationId={evaluationId}
          onSubmit={onSubmit}
          isSubmitting={isSubmitting}
        />
      </div>
    </div>
  );
}
