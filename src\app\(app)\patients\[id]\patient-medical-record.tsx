"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Calendar, 
  User, 
  ChevronDown, 
  ChevronRight,
  Stethoscope,
  ClipboardList,
  Activity
} from "lucide-react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface PatientMedicalRecordProps {
  evaluations: any[];
  appointments: any[];
}

export function PatientMedicalRecord({ evaluations, appointments }: PatientMedicalRecordProps) {
  const [openEvaluations, setOpenEvaluations] = React.useState<Record<number, boolean>>({});

  const toggleEvaluation = (id: number) => {
    setOpenEvaluations(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  return (
    <div className="space-y-6">
      

      {/* Historial de Evaluaciones */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Historial de Evaluaciones
            <Badge variant="secondary" className="ml-auto">
              {evaluations.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {evaluations.length > 0 ? (
            <div className="space-y-4">
              {evaluations.map((evaluation: any) => (
                <Collapsible
                  key={evaluation.id}
                  open={openEvaluations[evaluation.id]}
                  onOpenChange={() => toggleEvaluation(evaluation.id)}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-between p-4 h-auto border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-3 text-left">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Stethoscope className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">
                            Evaluación - {format(new Date(evaluation.createdAt), "PPP 'a las' HH:mm", { locale: es })}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Dr. {evaluation.doctor.name}
                            {evaluation.doctor.specialty && ` • ${evaluation.doctor.specialty}`}
                          </p>
                        </div>
                      </div>
                      {openEvaluations[evaluation.id] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="mt-2">
                    <div className="border rounded-lg p-4 bg-muted/20 space-y-4">
                      {/* Motivo de Consulta */}
                      {evaluation.consultationReason && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1 flex items-center gap-2">
                            <ClipboardList className="h-4 w-4" />
                            Motivo de Consulta
                          </h4>
                          <p className="text-sm">{evaluation.consultationReason}</p>
                        </div>
                      )}

                      {/* Enfermedad Actual */}
                      {evaluation.currentIllness && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1 flex items-center gap-2">
                            <Activity className="h-4 w-4" />
                            Enfermedad Actual
                          </h4>
                          <p className="text-sm">{evaluation.currentIllness}</p>
                        </div>
                      )}

                      {/* Signos Vitales */}
                      <div className="grid grid-cols-2 gap-4">
                        {evaluation.weight && (
                          <div>
                            <p className="text-xs text-muted-foreground">Peso</p>
                            <p className="text-sm font-medium">{evaluation.weight} kg</p>
                          </div>
                        )}
                        {evaluation.height && (
                          <div>
                            <p className="text-xs text-muted-foreground">Talla</p>
                            <p className="text-sm font-medium">{evaluation.height} cm</p>
                          </div>
                        )}
                        {evaluation.temperature && (
                          <div>
                            <p className="text-xs text-muted-foreground">Temperatura</p>
                            <p className="text-sm font-medium">{evaluation.temperature}°C</p>
                          </div>
                        )}
                        {evaluation.bloodPressure && (
                          <div>
                            <p className="text-xs text-muted-foreground">Presión Arterial</p>
                            <p className="text-sm font-medium">{evaluation.bloodPressure} mmHg</p>
                          </div>
                        )}
                      </div>

                      {/* Exámenes Paraclínicos */}
                      {evaluation.paraclinical && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">
                            Exámenes Paraclínicos
                          </h4>
                          <p className="text-sm">{evaluation.paraclinical}</p>
                        </div>
                      )}

                      {/* Impresión Diagnóstica */}
                      {evaluation.diagnosticImpression && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">
                            Impresión Diagnóstica
                          </h4>
                          <p className="text-sm">{evaluation.diagnosticImpression}</p>
                        </div>
                      )}

                      {/* Plan */}
                      {evaluation.actionPlan && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">
                            Plan
                          </h4>
                          <p className="text-sm">{evaluation.actionPlan}</p>
                        </div>
                      )}

                      {/* Exámenes de Laboratorio */}
                      {evaluation.evaluationLabExams && evaluation.evaluationLabExams.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">
                            Exámenes de Laboratorio
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {evaluation.evaluationLabExams.map((exam: any) => (
                              <a
                                key={exam.id}
                                href={exam.filePath} // Asumiendo que filePath es la URL pública
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs"
                              >
                                <Badge variant="outline">
                                  {exam.category || "Examen"}
                                </Badge>
                              </a>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              No hay evaluaciones registradas para este paciente
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
