import { PatientTable } from "./client-page";
import { Card, CardContent } from "@/components/ui/card";
import prisma from "@/lib/prisma";

type Patient = {
  id: string;
  recordNumber: string;
  name: string;
  idNumber: string;
  dob: string;
  gender: "Masculino" | "Femenino";
  insurance: string;
};

async function getPatients(): Promise<Patient[]> {
  try {
    const patients = await prisma.patient.findMany({
      include: {
        medicalRecords: true,
        appointments: {
          orderBy: {
            date: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return patients.map(patient => ({
      id: patient.id.toString(),
      recordNumber: patient.medicalRecords[0]?.record_number || 'N/A',
      name: patient.name,
      idNumber: patient.idCard || 'N/A',
      dob: patient.dob ? patient.dob.toISOString().split('T')[0] : 'N/A',
      gender: (patient.gender as "Masculino" | "Femenino") || "Masculino",
      insurance: patient.appointments[0]?.insurance || 'N/A'
    }));
  } catch (error) {
    console.error("Error fetching patients:", error);
    return [];
  }
}

export default async function PatientsPage() {
  const patients = await getPatients();

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-2xl font-bold font-headline">Pacientes Registrados</h1>
        <p className="text-muted-foreground mt-1">
          Lista de todos los pacientes en el sistema
        </p>
      </div>
      <Card>
        <CardContent className="p-4 sm:p-6">
          <PatientTable data={patients} />
        </CardContent>
      </Card>
    </div>
  );
}