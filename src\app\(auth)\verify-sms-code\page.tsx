"use client";

import React from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Smartphone, ArrowLeft, Loader2, CheckCircle } from "lucide-react";
import { GastroKidEvalLogo } from "@/components/icons";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";

export default function VerifySMSCodePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  const [phone, setPhone] = React.useState("");
  const [smsCode, setSmsCode] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);

  React.useEffect(() => {
    // Si viene de la página anterior, puede tener el teléfono en los parámetros
    const phoneParam = searchParams.get('phone');
    if (phoneParam) {
      setPhone(phoneParam);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/verify-sms-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, smsCode }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al verificar el código');
      }

      setIsSuccess(true);
      
      toast({
        title: "Código verificado",
        description: "Redirigiendo a la página de cambio de contraseña...",
      });

      // Redirigir a la página de reset con el token
      setTimeout(() => {
        router.push(`/reset-password?token=${data.resetToken}`);
      }, 2000);

    } catch (error: any) {
      setError(error.message);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 via-white to-fuchsia-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center bg-green-100 dark:bg-green-900/50 rounded-lg p-3 mb-4">
                <CheckCircle className="h-8 w-8 text-green-500 dark:text-green-400" />
              </div>
              <h1 className="text-3xl font-bold text-foreground">
                Código Verificado
              </h1>
              <p className="text-muted-foreground mt-2 text-sm">
                Redirigiendo a la página de cambio de contraseña...
              </p>
            </div>

            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Verificación exitosa</AlertTitle>
                <AlertDescription>
                  Su código ha sido verificado correctamente. En unos segundos será redirigido 
                  para establecer su nueva contraseña.
                </AlertDescription>
              </Alert>

              <div className="text-center">
                <Link href="/login">
                  <Button variant="ghost" className="text-sm">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Volver al inicio de sesión
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 via-white to-fuchsia-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <div className="w-full max-w-md">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center bg-sky-100 dark:bg-sky-900/50 rounded-lg p-3 mb-4">
              <GastroKidEvalLogo className="h-8 w-8 text-sky-500 dark:text-sky-400" />
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              Verificar Código SMS
            </h1>
            <p className="text-muted-foreground mt-2 text-sm">
              Ingrese el código de 6 dígitos que recibió por SMS.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <Terminal className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="phone" className="font-semibold">Número de Teléfono</Label>
              <div className="relative">
                <Smartphone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  id="phone"
                  type="tel"
                  placeholder="0983882267 o +593983882267"
                  required
                  className="pl-10"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="smsCode" className="font-semibold">Código de Verificación</Label>
              <Input
                id="smsCode"
                type="text"
                placeholder="123456"
                required
                maxLength={6}
                pattern="[0-9]{6}"
                className="text-center text-2xl font-mono tracking-widest"
                value={smsCode}
                onChange={(e) => setSmsCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground text-center">
                Ingrese el código de 6 dígitos que recibió por SMS
              </p>
            </div>
            
            <Button 
              type="submit" 
              size="lg" 
              className="w-full text-white font-bold bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 shadow-lg transition-all" 
              disabled={isLoading || smsCode.length !== 6}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verificando...
                </>
              ) : (
                "Verificar Código"
              )}
            </Button>

            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                ¿No recibió el código?
              </p>
              <Link href="/forgot-password">
                <Button variant="outline" className="text-sm">
                  Solicitar nuevo código
                </Button>
              </Link>
            </div>

            <div className="text-center">
              <Link href="/login">
                <Button variant="ghost" className="text-sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Volver al inicio de sesión
                </Button>
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
