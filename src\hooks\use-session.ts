'use client'

import React, { useEffect, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { UserSession } from '@/types/session'

export function useSession() {
  const [user, setUser] = useState<UserSession | null>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const checkSession = async () => {
      try {
        const response = await fetch("/api/auth/me");
        if (response.ok) {
          const data = await response.json();
          setUser(data);
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error("Error checking session:", error);
        setUser(null);
        toast({
          variant: "destructive",
          title: "Error de sesión",
          description: "No se pudo verificar la sesión. Por favor, intente de nuevo.",
        });
      } finally {
        setLoading(false);
      }
    };

    checkSession();
  }, [toast]);

  return {
    user,
    loading,
    isAuthenticated: !!user,
  }
}