
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of the current day in local time

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const count = await prisma.evaluation.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
      },
    });

    const nextNumber = count + 1;

    // You might want to format this number, e.g., with leading zeros
    // For now, we'll return it as is.
    return NextResponse.json({ nextNumber });

  } catch (error) {
    console.error('Error getting next attention number:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}
