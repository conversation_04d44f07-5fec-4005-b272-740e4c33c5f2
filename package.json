{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9003", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.11.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.19.2", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.475.0", "next": "^15.3.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.1", "@types/react": "^18.3.23", "@types/react-dom": "^18", "@types/react-table": "^7.7.20", "eslint": "^9.30.1", "eslint-config-next": "^15.3.4", "postcss": "^8", "prisma": "^6.11.0", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}