// src/app/api/auth/register/route.ts
import { NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { hashPassword } from '@/lib/auth/auth'

export async function POST(req: Request) {
  const data = await req.json()
  const { email, password, name, alias, role, ...rest } = data

  if (!email || !password || !name || !alias) {
    return NextResponse.json({ error: 'Faltan campos obligatorios' }, { status: 400 })
  }

  const existing = await prisma.user.findUnique({ where: { email } })
  if (existing) {
    return NextResponse.json({ error: 'El usuario ya existe' }, { status: 409 })
  }

  const hashed = await hashPassword(password)

  const user = await prisma.user.create({
    data: {
      email,
      password: hashed,
      name,
      alias,
      role,
      active: true,
      ...rest
    },
  })

  return NextResponse.json({
    message: 'Usuario registrado correctamente',
    user: {
      id: user.id,
      email: user.email,
      alias: user.alias,
      role: user.role,
    },
  })
}