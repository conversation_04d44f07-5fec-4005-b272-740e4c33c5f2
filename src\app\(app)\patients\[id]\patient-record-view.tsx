"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileText, User, Calendar, Phone, MapPin, Briefcase } from "lucide-react";
import Link from "next/link";
import { PatientMedicalRecord } from "./patient-medical-record";
import { NewEvaluationForm } from "./new-evaluation-form";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { es } from "date-fns/locale";

interface PatientRecordViewProps {
  patient: any; // Tipo completo del paciente con todas las relaciones
}

export function PatientRecordView({ patient }: PatientRecordViewProps) {
  const [activeTab, setActiveTab] = React.useState<"record" | "evaluation">("record");

  const calculateAge = (dob: Date | null) => {
    if (!dob) return "N/A";
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return `${age} años`;
  };

  const mother = patient.parents?.find((p: any) => p.type === "mother");
  const father = patient.parents?.find((p: any) => p.type === "father");

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Link href="/patients">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver a Pacientes
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold font-headline">{patient.name}</h1>
              <p className="text-muted-foreground">
                Expediente: {patient.medicalRecords[0]?.record_number || "N/A"} • 
                Edad: {calculateAge(patient.dob)}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant={activeTab === "record" ? "default" : "outline"}
              onClick={() => setActiveTab("record")}
              size="sm"
            >
              <FileText className="h-4 w-4 mr-2" />
              Expediente
            </Button>
            <Button
              variant={activeTab === "evaluation" ? "default" : "outline"}
              onClick={() => setActiveTab("evaluation")}
              size="sm"
            >
              <User className="h-4 w-4 mr-2" />
              Nueva Evaluación
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 overflow-hidden">
        {/* Panel Izquierdo - Expediente */}
        <div className="space-y-6 overflow-y-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-primary" />
                Información del Paciente
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Nombre Completo</p>
                  <p className="font-medium">{patient.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Cédula</p>
                  <p className="font-medium">{patient.idCard || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Género</p>
                  <p className="font-medium">{patient.gender || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Fecha de Nacimiento</p>
                  <p className="font-medium">
                    {patient.dob ? format(new Date(patient.dob), "PPP", { locale: es }) : "N/A"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Información de Padres */}
          {(mother || father) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  Información de Padres
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {mother && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Madre</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium">Nombre:</span> {mother.name}
                      </div>
                      <div>
                        <span className="font-medium">Edad:</span> {mother.age || "N/A"}
                      </div>
                      <div>
                        <span className="font-medium">Teléfono:</span> {mother.phone || "N/A"}
                      </div>
                      <div>
                        <span className="font-medium">Ocupación:</span> {mother.occupation || "N/A"}
                      </div>
                      {mother.address && (
                        <div className="col-span-2">
                          <span className="font-medium">Dirección:</span> {mother.address}
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {father && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Padre</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium">Nombre:</span> {father.name}
                      </div>
                      <div>
                        <span className="font-medium">Edad:</span> {father.age || "N/A"}
                      </div>
                      <div>
                        <span className="font-medium">Teléfono:</span> {father.phone || "N/A"}
                      </div>
                      <div>
                        <span className="font-medium">Ocupación:</span> {father.occupation || "N/A"}
                      </div>
                      {father.address && (
                        <div className="col-span-2">
                          <span className="font-medium">Dirección:</span> {father.address}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Historial de Evaluaciones */}
          <PatientMedicalRecord 
            evaluations={patient.evaluations} 
            appointments={patient.appointments}
          />
        </div>

        {/* Panel Derecho - Nueva Evaluación */}
        <div className="overflow-y-auto">
          <NewEvaluationForm 
            patient={patient}
            onEvaluationSaved={() => {
              // Recargar la página para mostrar la nueva evaluación
              window.location.reload();
            }}
          />
        </div>
      </div>
    </div>
  );
}
