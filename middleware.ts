import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth/auth'

export function middleware(req: NextRequest) {
  const token = req.cookies.get('token')?.value

  // Si no hay token, redirige al login
  if (!token) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  try {
    const decoded = verifyToken(token)
    // Si el token es inválido o expirado, verifyToken devuelve null
    if (!decoded) {
      // Borra la cookie inválida y redirige al login
      const response = NextResponse.redirect(new URL('/login', req.url))
      response.cookies.delete('token')
      return response
    }

    // El token es válido, permite continuar
    return NextResponse.next()
  } catch (err) {
    // En caso de cualquier otro error, redirige al login
    const response = NextResponse.redirect(new URL('/login', req.url))
    response.cookies.delete('token')
    return response
  }
}

export const config = {
  matcher: ['/app/:path*'],
}