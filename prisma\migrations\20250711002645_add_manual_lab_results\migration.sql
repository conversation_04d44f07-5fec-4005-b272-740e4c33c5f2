-- CreateTable
CREATE TABLE "ManualLabResult" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "reference" TEXT,
    "evaluationId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ManualLabResult_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ManualLabResult" ADD CONSTRAINT "ManualLabResult_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES "Evaluation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
