# **App Name**: PediEval

## Core Features:

- Data Capture Forms: Display forms to capture patient and parental data (name, ID, DOB, contact info, etc.).
- Medical Evaluation Forms: Present dedicated sections for recording patient history, symptoms, examination results, and diagnostic impressions.
- Clinical Information Input: Provide fields for detailing consultation reasons, current illnesses, and high/low digestive symptoms.
- User Authentication: Implement a secure, role-based user authentication system.

## Style Guidelines:

- Primary color: Soft lavender (#D1C4E9) to evoke a sense of calm and medical trustworthiness.
- Background color: Light gray-blue (#ECEFF1) for a clean, clinical feel.
- Accent color: Pale violet (#BBDEFB) to highlight key interactive elements.
- Body and headline font: 'Nunito' (sans-serif) for a modern, readable interface. Note: currently only Google Fonts are supported.
- Use minimalist, clear icons to represent different sections and actions.
- Design a clear and intuitive layout for easy navigation across sections.
- Use subtle transitions to enhance user experience when navigating between forms and sections.