const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Hash de la contraseña
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Crear usuario administrador
    const user = await prisma.user.create({
      data: {
        name: 'Administrador',
        email: '<EMAIL>',
        alias: 'Admin',
        password: hashedPassword,
        role: 'admin',
        active: true,
        specialty: 'Administración',
        msp: 'ADMIN-001',
        phone: '0999999999',
      },
    });

    console.log('✅ Usuario administrador creado exitosamente:');
    console.log(`📧 Email: ${user.email}`);
    console.log(`🔑 Contraseña: admin123`);
    console.log(`👤 Alias: ${user.alias}`);
    console.log(`🔧 Rol: ${user.role}`);

  } catch (error) {
    if (error.code === 'P2002') {
      console.log('ℹ️  El usuario administrador ya existe en la base de datos');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Contraseña: admin123');
    } else {
      console.error('❌ Error creando usuario administrador:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
