
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
  }

  try {
    const patients = await prisma.patient.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { idCard: { contains: query, mode: 'insensitive' } },
          { alias: { contains: query, mode: 'insensitive' } },
          {
            medicalRecords: {
              some: {
                record_number: { contains: query, mode: 'insensitive' },
              },
            },
          },
        ],
      },
      include: {
        medicalRecords: true, // Include medical records to get the history number
        evaluations: {
          orderBy: {
            createdAt: 'desc', // Order by creation date descending to get the latest
          },
        },
      },
    });

    return NextResponse.json(patients);
  } catch (error) {
    console.error('Error searching patients:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}
