"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Search, FileDown, Loader2, CalendarIcon } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { certificateSchema } from "./schemas";
import { CertificatePreview } from "./certificate-preview";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useSession } from "@/hooks/use-session";
import { cn } from "@/lib/utils";

// Define Patient type based on Prisma schema
type Patient = {
  id: number;
  alias: string;
  name: string;
  idCard: string | null;
  gender: string | null;
  dob: string | null; // Comes as string from JSON
  age: string | null;
  createdAt: string;
  updatedAt: string;
  medicalRecords?: { id: number; record_number: string }[]; // Add medicalRecords
};

export function CertificateForm() {
  const { user, loading: sessionLoading } = useSession();
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [submittedData, setSubmittedData] =
    React.useState<z.infer<typeof certificateSchema> | null>(null);

  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchResults, setSearchResults] = React.useState<Patient[]>([]);
  const [isSearching, setIsSearching] = React.useState(false);

  const form = useForm<z.infer<typeof certificateSchema>>({
    resolver: zodResolver(certificateSchema),
    defaultValues: {
      patientSearch: "",
      patientName: "",
      idCard: "",
      clinicHistoryNumber: "",
      age: "",
      address: "",
      phone: "",
      attentionNumber: "Cargando...",
      attentionDateNumeric: format(new Date(), "dd/MM/yyyy"),
      attentionDateWritten: format(new Date(), "d 'de' MMMM 'de' yyyy", {
        locale: es,
      }),
      attendedAt: "CONSULTA EXTERNA de esta casa de salud",
      diagnosis: "",
      procedures: "",
      observations: "",
      workplace: "",
      workActivity: "",
      contingencyType: "Enfermedad general",
      hasSymptoms: undefined,
    },
  });

  // Fetch next attention number on mount
  React.useEffect(() => {
    fetch("/api/attentions/next-number")
      .then((res) => res.json())
      .then((data) => {
        if (data.nextNumber) {
          form.setValue("attentionNumber", data.nextNumber.toString());
        }
      })
      .catch(() => {
        toast({
          variant: "destructive",
          title: "Error",
          description: "No se pudo obtener el número de atención.",
        });
        form.setValue("attentionNumber", "Error");
      });
  }, [form]);

  // Debounced search effect
  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (searchQuery.length > 2) {
        setIsSearching(true);
        fetch(`/api/patients/search?query=${searchQuery}`)
          .then((res) => res.json())
          .then((data) => {
            setSearchResults(data);
          })
          .catch((err) => {
            console.error("Failed to search patients", err);
            toast({
              variant: "destructive",
              title: "Error de Búsqueda",
              description: "No se pudieron buscar los pacientes.",
            });
          })
          .finally(() => {
            setIsSearching(false);
          });
      } else {
        setSearchResults([]);
      }
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  const handleSelectPatient = (patient: Patient) => {
    form.setValue("patientName", patient.name);
    form.setValue("idCard", patient.idCard || "");
    form.setValue(
      "clinicHistoryNumber",
      patient.medicalRecords && patient.medicalRecords.length > 0
        ? patient.medicalRecords[0].record_number
        : ""
    );
    form.setValue("age", patient.age || "");
    // Address and phone are not in the Patient model directly
    // You might need to fetch related Parent data if required
    form.setValue("address", ""); // Clear or handle as needed
    form.setValue("phone", ""); // Clear or handle as needed

    setSearchQuery("");
    setSearchResults([]);
  };

  const [attentionDate, setAttentionDate] = React.useState<Date | undefined>(
    () => {
      const dateStr = form.getValues("attentionDateNumeric");
      if (dateStr) {
        const [day, month, year] = dateStr.split("/").map(Number);
        if (day && month && year) {
          return new Date(year, month - 1, day);
        }
      }
      return new Date();
    }
  );

  const handleDateChange = (date: Date | undefined) => {
    setAttentionDate(date);
    if (date) {
      form.setValue("attentionDateNumeric", format(date, "dd/MM/yyyy"));
      form.setValue(
        "attentionDateWritten",
        format(date, "d 'de' MMMM 'de' yyyy", { locale: es })
      );
    } else {
      form.setValue("attentionDateNumeric", "");
      form.setValue("attentionDateWritten", "");
    }
  };

  function onSubmit(values: z.infer<typeof certificateSchema>) {
    setSubmittedData(values);
  }

  React.useEffect(() => {
    if (submittedData) {
      setIsGenerating(true);
      toast({
        title: "Generando PDF...",
        description: "Por favor, espere un momento.",
      });

      const generatePdf = async () => {
        const input = document.getElementById("pdf-content");
        if (!input) {
          toast({
            variant: "destructive",
            title: "Error",
            description:
              "No se pudo encontrar el contenido para generar el PDF.",
          });
          setIsGenerating(false);
          setSubmittedData(null);
          return;
        }

        try {
          const canvas = await html2canvas(input, {
            scale: 2.5,
            useCORS: true,
          });

          const imgData = canvas.toDataURL("image/png");
          const pdf = new jsPDF("p", "mm", "a4");
          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          const canvasWidth = canvas.width;
          const canvasHeight = canvas.height;
          const ratio = canvasWidth / canvasHeight;

          let imgWidth = pdfWidth - 20;
          let imgHeight = imgWidth / ratio;

          if (imgHeight > pdfHeight - 20) {
            imgHeight = pdfHeight - 20;
            imgWidth = imgHeight * ratio;
          }

          const x = (pdfWidth - imgWidth) / 2;
          const y = 10;

          pdf.addImage(imgData, "PNG", x, y, imgWidth, imgHeight);
          pdf.save(
            `certificado-medico-${
              submittedData.patientName?.trim().replace(/\s/g, "_") ||
              "paciente"
            }.pdf`
          );

          toast({
            title: "Certificado Generado",
            description: "El PDF se ha descargado exitosamente.",
          });
        } catch (error) {
          console.error("Error generating PDF:", error);
          toast({
            variant: "destructive",
            title: "Error al generar PDF",
            description:
              "Ocurrió un problema al crear el documento. Intente de nuevo.",
          });
        } finally {
          setIsGenerating(false);
          setSubmittedData(null);
        }
      };

      const timer = setTimeout(generatePdf, 300);
      return () => clearTimeout(timer);
    }
  }, [submittedData]);

  return (
    <Form {...form}>
      <div className="absolute top-0 left-[-9999px] z-[-1] opacity-0">
        {submittedData && (
          <CertificatePreview data={submittedData} doctorInfo={user} />
        )}
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Buscar Paciente</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="patientSearch"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">Buscar Paciente</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Buscar por nombre, cédula o historia clínica..."
                        className="pl-10"
                        {...field}
                        value={searchQuery}
                        onChange={(e) => {
                          field.onChange(e);
                          setSearchQuery(e.target.value);
                        }}
                      />
                      {isSearching && (
                        <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin" />
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                  {searchResults.length > 0 && (
                    <div className="relative">
                      <ul className="absolute w-full bg-card border border-border rounded-md mt-1 z-10 max-h-60 overflow-y-auto">
                        {searchResults.map((patient) => (
                          <li
                            key={patient.id}
                            className="px-4 py-2 hover:bg-muted cursor-pointer"
                            onClick={() => handleSelectPatient(patient)}
                          >
                            <p className="font-semibold">{patient.name}</p>
                            <p className="text-sm text-muted-foreground">
                              C.I: {patient.idCard || "N/A"} - H.C:{" "}
                              {patient.medicalRecords &&
                              patient.medicalRecords.length > 0
                                ? patient.medicalRecords[0].record_number
                                : "N/A"}
                            </p>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Datos del Paciente y Atención</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FormField
              control={form.control}
              name="patientName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>El/la paciente</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="idCard"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cédula / Pasaporte</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="clinicHistoryNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Historia Clínica No.</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="age"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Edad</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dirección</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly placeholder="Seleccione un paciente para autocompletar"/>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Teléfono</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly placeholder="Seleccione un paciente para autocompletar"/>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="attentionNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Atención No.</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="attentionDateNumeric"
              render={({ field }) => (
                <FormItem className="flex flex-col md:col-span-2">
                  <FormLabel>Fecha de Atención</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !attentionDate && "text-muted-foreground"
                          )}
                        >
                          {attentionDate ? (
                            format(attentionDate, "PPP", { locale: es })
                          ) : (
                            <span>Seleccionar fecha</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={attentionDate}
                        onSelect={handleDateChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="attendedAt"
              render={({ field }) => (
                <FormItem className="md:col-span-3">
                  <FormLabel>Fue atendido/a en</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Información Clínica</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="diagnosis"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Diagnóstico(s)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Ingrese el diagnóstico..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="procedures"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Procedimiento(s) - Fecha(s)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describa procedimientos y fechas..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="observations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observaciones</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Ingrese observaciones adicionales..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Información Adicional Referida por Paciente</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="workplace"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lugar de Trabajo (paciente)</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: Unidad Educativa X" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="workActivity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Actividad Laboral (paciente)</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: Estudiante" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="contingencyType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Contingencia</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="hasSymptoms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Presenta Síntomas</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex items-center space-x-4 pt-2"
                      >
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="Sí" />
                          </FormControl>
                          <FormLabel className="font-normal">Sí</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="No" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="h-28" /> {/* Spacer for signature and stamp */}
            <div className="border-t-2 border-dashed border-muted-foreground w-1/2 mx-auto pt-2 text-center">
              <p className="text-sm text-muted-foreground">
                Firma / Sello del Médico
              </p>
            </div>
            {sessionLoading ? (
              <div className="text-center mt-4 text-sm text-muted-foreground">
                <p>Cargando firma...</p>
              </div>
            ) : user ? (
              <div className="text-center mt-4 text-sm text-muted-foreground">
                <p className="font-semibold text-foreground">{user.name}</p>
                <p>{user.specialty || "Especialidad no especificada"}</p>
                <p>MSP: {user.msp || "N/A"}</p>
                <p>Email: {user.email}</p>
              </div>
            ) : (
              <div className="text-center mt-4 text-sm text-destructive">
                <p>No se pudo cargar la información del médico.</p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="submit" disabled={isGenerating || sessionLoading}>
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <FileDown className="mr-2 h-4 w-4" />
                Generar y Descargar PDF
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}