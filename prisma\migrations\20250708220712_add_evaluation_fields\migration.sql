/*
  Warnings:

  - Added the required column `updatedAt` to the `Appointment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Evaluation` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `EvaluationLabExam` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `Parent` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Parent` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Patient` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Appointment" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "insurance" TEXT,
ADD COLUMN     "pediatrician" TEXT,
ADD COLUMN     "recordNumber" TEXT,
ADD COLUMN     "referrer" TEXT,
ADD COLUMN     "time" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "Evaluation" ADD COLUMN     "actionPlan" TEXT,
ADD COLUMN     "bloodPressure" TEXT,
ADD COLUMN     "bowelHabits" TEXT,
ADD COLUMN     "cardiacFrequency" TEXT,
ADD COLUMN     "consultationReason" TEXT,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "currentIllness" TEXT,
ADD COLUMN     "developmentHistory" TEXT,
ADD COLUMN     "diagnosticImpression" TEXT,
ADD COLUMN     "familyMedicalHistory" TEXT,
ADD COLUMN     "generalObservations" TEXT,
ADD COLUMN     "headCircumference" TEXT,
ADD COLUMN     "height" TEXT,
ADD COLUMN     "immunizations" TEXT,
ADD COLUMN     "lowerDigestiveSymptoms" TEXT,
ADD COLUMN     "nutritionalHistory" TEXT,
ADD COLUMN     "oxygenSaturation" TEXT,
ADD COLUMN     "paraclinical" TEXT,
ADD COLUMN     "perinatalHistory" TEXT,
ADD COLUMN     "personalMedicalHistory" TEXT,
ADD COLUMN     "physicalExam" TEXT,
ADD COLUMN     "systemsReview" TEXT,
ADD COLUMN     "temperature" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "upperDigestiveSymptoms" TEXT,
ADD COLUMN     "weight" TEXT;

-- AlterTable
ALTER TABLE "EvaluationLabExam" ADD COLUMN     "category" TEXT,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "fileName" TEXT,
ADD COLUMN     "filePath" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "Parent" ADD COLUMN     "address" TEXT,
ADD COLUMN     "age" TEXT,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "occupation" TEXT,
ADD COLUMN     "phone" TEXT,
ADD COLUMN     "type" TEXT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "Patient" ADD COLUMN     "age" TEXT,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "dob" TIMESTAMP(3),
ADD COLUMN     "gender" TEXT,
ADD COLUMN     "idCard" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;
