

import { cookies } from 'next/headers'
import { verifyToken } from '@/lib/auth/auth'
import { SessionProvider } from '@/contexts/session-context'
import ClientAppLayout from './client-layout'
import type { UserSession } from '@/types/session'

export const metadata = {
  title: 'GastroKid Eval',
  description: 'Evaluación clínica pediátrica especializada',
}

export default async function AppLayout({ children }: { children: React.ReactNode }) {
  const cookieStore = await cookies() // ← necesita await
  const token = cookieStore.get('token')?.value
  let user: UserSession | null = null

  if (token) {
    try {
      const verified = verifyToken(token)
      if (
        verified &&
        typeof verified === 'object' &&
        !Array.isArray(verified) &&
        'userId' in verified &&
        'alias' in verified &&
        'role' in verified
      ) {
        user = verified as UserSession
      }
    } catch {
      user = null
    }
  }

  return (
    <SessionProvider initialUser={user}>
      <ClientAppLayout>{children}</ClientAppLayout>
    </SessionProvider>
  )
}