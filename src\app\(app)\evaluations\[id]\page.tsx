import { EditEvaluationForm } from "./edit-evaluation-form";
import prisma from "@/lib/prisma";
import { notFound } from "next/navigation";

async function getEvaluationWithDetails(id: string) {
  try {
    const evaluation = await prisma.evaluation.findUnique({
      where: { id: parseInt(id) },
      include: {
        patient: {
          include: {
            parents: true,
            medicalRecords: true,
            appointments: {
              orderBy: {
                date: 'desc'
              },
              take: 1
            }
          }
        },
        doctor: {
          select: {
            name: true,
            specialty: true
          }
        },
        evaluationLabExams: true
      }
    });

    if (!evaluation) {
      return null;
    }

    // Buscar la cita más reciente del paciente para obtener información adicional
    const latestAppointment = evaluation.patient.appointments[0];

    // Construir el objeto de datos para el formulario
    const formData = {
      appointment: {
        date: latestAppointment?.date || new Date(),
        time: latestAppointment?.time || "",
        recordNumber: evaluation.patient.medicalRecords[0]?.record_number || "",
        insurance: latestAppointment?.insurance || "",
        pediatrician: latestAppointment?.pediatrician || "",
        referrer: latestAppointment?.referrer || "",
      },
      patient: {
        name: evaluation.patient.name,
        idCard: evaluation.patient.idCard || "",
        gender: evaluation.patient.gender || "",
        dob: evaluation.patient.dob,
        age: evaluation.patient.age || "",
      },
      mother: evaluation.patient.parents?.find(p => p.type === "mother") || {
        name: "",
        age: "",
        address: "",
        phone: "",
        occupation: "",
      },
      father: evaluation.patient.parents?.find(p => p.type === "father") || {
        name: "",
        age: "",
        address: "",
        phone: "",
        occupation: "",
      },
      medical: {
        consultationReason: evaluation.consultationReason || "",
        currentIllness: evaluation.currentIllness || "",
        upperDigestiveSymptoms: evaluation.upperDigestiveSymptoms || "",
        lowerDigestiveSymptoms: evaluation.lowerDigestiveSymptoms || "",
        bowelHabits: evaluation.bowelHabits || "",
        weight: evaluation.weight || "",
        height: evaluation.height || "",
        headCircumference: evaluation.headCircumference || "",
        bloodPressure: evaluation.bloodPressure || "",
        temperature: evaluation.temperature || "",
        cardiacFrequency: evaluation.cardiacFrequency || "",
        oxygenSaturation: evaluation.oxygenSaturation || "",
        perinatalHistory: evaluation.perinatalHistory || "",
        nutritionalHistory: evaluation.nutritionalHistory || "",
        developmentHistory: evaluation.developmentHistory || "",
        immunizations: evaluation.immunizations || "",
        personalMedicalHistory: evaluation.personalMedicalHistory || "",
        familyMedicalHistory: evaluation.familyMedicalHistory || "",
        systemsReview: evaluation.systemsReview || "",
        physicalExam: evaluation.physicalExam || "",
        analysis: evaluation.analysis || "",
        paraclinical: evaluation.paraclinical || "",
        diagnosticImpression: evaluation.diagnosticImpression || "",
        actionPlan: evaluation.actionPlan || "",
      },
      labFiles: evaluation.evaluationLabExams.map(exam => ({
        file: null, // Los archivos no se pueden recuperar
        category: exam.category || "Examen de Laboratorio (PDF)",
      })),
    };

    return {
      evaluation,
      formData
    };
  } catch (error) {
    console.error("Error fetching evaluation:", error);
    return null;
  }
}

interface EditEvaluationPageProps {
  params: {
    id: string;
  };
}

export default async function EditEvaluationPage({ params }: EditEvaluationPageProps) {
  const { id } = await params;
  console.log("EditEvaluationPage - evaluationId:", id);

  const data = await getEvaluationWithDetails(id);
  console.log("EditEvaluationPage - data loaded:", !!data);
  console.log("EditEvaluationPage - formData:", data?.formData);

  if (!data) {
    notFound();
  }

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-2xl font-bold font-headline">
          Editar Evaluación - {data.evaluation.patient.name}
        </h1>
        <p className="text-muted-foreground mt-1">
          Modifica los datos de la evaluación médica
        </p>
      </div>
      <EditEvaluationForm 
        evaluationId={data.evaluation.id}
        initialData={data.formData}
      />
    </div>
  );
}
