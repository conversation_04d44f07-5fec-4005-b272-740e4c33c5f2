"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Trash2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { deletePatient } from "./actions";

interface DeletePatientDialogProps {
  patient: {
    id: string;
    name: string;
  } | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onPatientDeleted: () => void;
}

export function DeletePatientDialog({
  patient,
  isOpen,
  onOpenChange,
  onPatientDeleted,
}: DeletePatientDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDelete = async () => {
    if (!patient) return;

    setIsDeleting(true);
    
    try {
      const result = await deletePatient(parseInt(patient.id));
      
      if (result.success) {
        toast({
          title: "Paciente Eliminado",
          description: result.message,
        });
        onOpenChange(false);
        onPatientDeleted();
      } else {
        toast({
          variant: "destructive",
          title: "Error al eliminar",
          description: result.message,
        });
      }
    } catch (error) {
      console.error("Error deleting patient:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Ocurrió un error al eliminar el paciente.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!patient) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Eliminar Paciente
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              ¿Estás seguro de que deseas eliminar al paciente{" "}
              <strong className="text-foreground">{patient.name}</strong>?
            </p>
            <p className="text-sm text-muted-foreground">
              Esta acción no se puede deshacer. Se eliminarán todos los datos
              asociados al paciente, incluyendo:
            </p>
            <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
              <li>Información personal y de contacto</li>
              <li>Todas las evaluaciones médicas registradas</li>
              <li>Registros médicos y exámenes de laboratorio</li>
              <li>Citas programadas</li>
              <li>Información de padres/tutores</li>
            </ul>
            <p className="text-sm font-medium text-red-600">
              ⚠️ Advertencia: Esta acción eliminará permanentemente todas las
              evaluaciones médicas y datos clínicos del paciente.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Eliminando...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar Paciente
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
