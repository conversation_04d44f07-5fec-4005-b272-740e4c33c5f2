"use client";

import * as React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Mail, ArrowLeft, Loader2, Smartphone } from "lucide-react";
import { GastroKidEvalLogo } from "@/components/icons";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal, CheckCircle } from "lucide-react";

export default function ForgotPasswordPage() {
  const { toast } = useToast();
  const [email, setEmail] = React.useState("");
  const [phone, setPhone] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const [resetUrl, setResetUrl] = React.useState<string | null>(null);
  const [smsCode, setSmsCode] = React.useState<string | null>(null);
  const [recoveryMethod, setRecoveryMethod] = React.useState<"email" | "sms">("email");

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al enviar el correo de recuperación');
      }

      setIsSuccess(true);
      setRecoveryMethod("email");

      // En desarrollo, guardar el enlace para mostrarlo
      if (data.resetUrl) {
        setResetUrl(data.resetUrl);
      }

      toast({
        title: "Correo enviado",
        description: "Se ha enviado un enlace de recuperación a su correo electrónico.",
      });

    } catch (error: any) {
      setError(error.message);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSMSSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/forgot-password-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al enviar el código SMS');
      }

      setIsSuccess(true);
      setRecoveryMethod("sms");

      // En desarrollo, guardar el código para mostrarlo
      if (data.smsCode) {
        setSmsCode(data.smsCode);
      }

      toast({
        title: "SMS enviado",
        description: "Se ha enviado un código de verificación a su teléfono.",
      });

    } catch (error: any) {
      setError(error.message);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="p-1 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
          <div className="bg-card rounded-xl p-6 sm:p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center bg-green-100 dark:bg-green-900/50 rounded-lg p-3 mb-4">
                <CheckCircle className="h-8 w-8 text-green-500 dark:text-green-400" />
              </div>
              <h1 className="text-3xl font-bold text-foreground">
                {recoveryMethod === "email" ? "Correo Enviado" : "SMS Enviado"}
              </h1>
              <p className="text-muted-foreground mt-2 text-sm">
                {recoveryMethod === "email"
                  ? `Se ha enviado un enlace de recuperación a ${email}`
                  : `Se ha enviado un código de verificación a ${phone}`
                }
              </p>
            </div>

            <div className="space-y-4">
              {recoveryMethod === "email" ? (
                <>
                  <Alert>
                    <Mail className="h-4 w-4" />
                    <AlertTitle>Revise su correo electrónico</AlertTitle>
                    <AlertDescription>
                      Hemos enviado un enlace de recuperación de contraseña a su correo electrónico.
                      El enlace expirará en 1 hora.
                    </AlertDescription>
                  </Alert>

                  {/* Mostrar enlace directo en desarrollo */}
                  {resetUrl && (
                    <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                      <Terminal className="h-4 w-4 text-blue-600" />
                      <AlertTitle className="text-blue-800 dark:text-blue-200">Modo Desarrollo</AlertTitle>
                      <AlertDescription className="text-blue-700 dark:text-blue-300">
                        <p className="mb-2">Como estás en modo desarrollo, aquí tienes el enlace directo:</p>
                        <Link
                          href={resetUrl}
                          className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                        >
                          Ir a restablecer contraseña
                        </Link>
                      </AlertDescription>
                    </Alert>
                  )}
                </>
              ) : (
                <>
                  <Alert>
                    <Smartphone className="h-4 w-4" />
                    <AlertTitle>Revise su teléfono</AlertTitle>
                    <AlertDescription>
                      Hemos enviado un código de verificación de 6 dígitos a su teléfono.
                      El código expirará en 10 minutos.
                    </AlertDescription>
                  </Alert>

                  {/* Mostrar código directo en desarrollo */}
                  {smsCode && (
                    <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
                      <Terminal className="h-4 w-4 text-green-600" />
                      <AlertTitle className="text-green-800 dark:text-green-200">Modo Desarrollo</AlertTitle>
                      <AlertDescription className="text-green-700 dark:text-green-300">
                        <p className="mb-2">Como estás en modo desarrollo, aquí tienes el código:</p>
                        <div className="bg-green-600 text-white px-4 py-2 rounded font-mono text-lg text-center">
                          {smsCode}
                        </div>
                        <p className="mt-2 text-sm">Usa este código en la página de verificación.</p>
                      </AlertDescription>
                    </Alert>
                  )}
                </>
              )}

              <div className="text-center space-y-4">
                <p className="text-sm text-muted-foreground">
                  {recoveryMethod === "email"
                    ? "¿No recibió el correo? Revise su carpeta de spam o intente nuevamente."
                    : "¿No recibió el SMS? Verifique su número o intente nuevamente."
                  }
                </p>

                {recoveryMethod === "sms" && !smsCode && (
                  <Link href="/verify-sms-code">
                    <Button className="w-full">
                      Ingresar código de verificación
                    </Button>
                  </Link>
                )}

                <Button
                  variant="outline"
                  onClick={() => {
                    setIsSuccess(false);
                    setEmail("");
                    setPhone("");
                    setResetUrl(null);
                    setSmsCode(null);
                  }}
                  className="w-full"
                >
                  Enviar nuevamente
                </Button>

                <Link href="/login">
                  <Button variant="ghost" className="w-full">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Volver al inicio de sesión
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="p-1 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
        <div className="bg-card rounded-xl p-6 sm:p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center bg-sky-100 dark:bg-sky-900/50 rounded-lg p-3 mb-4">
              <GastroKidEvalLogo className="h-8 w-8 text-sky-500 dark:text-sky-400" />
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              Recuperar Contraseña
            </h1>
            <p className="text-muted-foreground mt-2 text-sm">
              Elija su método de recuperación preferido.
            </p>
          </div>

          <Tabs defaultValue="email" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Correo Electrónico
              </TabsTrigger>
              <TabsTrigger value="sms" className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                SMS
              </TabsTrigger>
            </TabsList>

            {error && (
              <Alert variant="destructive">
                <Terminal className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <TabsContent value="email">
              <form onSubmit={handleEmailSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="font-semibold">Correo Electrónico</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      className="pl-10"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full text-white font-bold bg-gradient-to-r from-blue-500 to-fuchsia-500 hover:from-blue-600 hover:to-fuchsia-600 shadow-lg transition-all"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    "Enviar Enlace de Recuperación"
                  )}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="sms">
              <form onSubmit={handleSMSSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="font-semibold">Número de Teléfono</Label>
                  <div className="relative">
                    <Smartphone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="0983882267 o +593983882267"
                      required
                      className="pl-10"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Ingrese su número de teléfono registrado en el sistema
                  </p>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full text-white font-bold bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 shadow-lg transition-all"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    "Enviar Código SMS"
                  )}
                </Button>
              </form>
            </TabsContent>

            <div className="text-center">
              <Link href="/login">
                <Button variant="ghost" className="text-sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Volver al inicio de sesión
                </Button>
              </Link>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
