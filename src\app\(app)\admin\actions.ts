
"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { createUserSchema, updateUserSchema } from "./schemas";
import prisma from "@/lib/prisma";
import { hashPassword } from "@/lib/auth/auth";

export async function addUser(data: z.infer<typeof createUserSchema>) {
  try {
    const validatedData = createUserSchema.parse(data);
    const hashedPassword = await hashPassword(validatedData.password);

    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        alias: validatedData.alias,
        role: validatedData.role,
        specialty: validatedData.specialty,
        msp: validatedData.msp,
        phone: validatedData.phone,
        password: hashedPassword,
        active: true,
      } as any,
    });

    console.log(`User added with ID: ${user.id}`);

    revalidatePath("/admin");

    return {
      success: true,
      message: `Usuario "${validatedData.name}" creado exitosamente.`,
    };
  } catch (error) {
    console.error("Error adding user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Datos inválidos. Por favor revise el formulario.",
      };
    }

    return {
      success: false,
      message: "Ocurrió un error al crear el usuario.",
    };
  }
}

export async function updateUser(userId: number, data: z.infer<typeof updateUserSchema>) {
  try {
    const validatedData = updateUserSchema.parse(data);
    console.log("Received data for update:", validatedData);
    const { password, ...userData } = validatedData;

    if (password && password !== "") {
      (userData as any).password = await hashPassword(password);
    }

    console.log("Data to be updated in Prisma:", userData);

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: userData as any,
    });

    console.log(`User updated with ID: ${updatedUser.id}`);

    revalidatePath("/admin");

    return {
      success: true,
      message: `Usuario "${validatedData.name}" actualizado exitosamente.`,
    };
  } catch (error) {
    console.error("Error updating user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Datos inválidos. Por favor revise el formulario.",
      };
    }

    return {
      success: false,
      message: "Ocurrió un error al actualizar el usuario.",
    };
  }
}
