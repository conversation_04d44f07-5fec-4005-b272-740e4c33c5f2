import * as z from "zod";

export const medicationSchema = z.object({
  name: z.string().min(1, "El nombre del medicamento es requerido."),
  presentation: z.string().min(1, "La presentación es requerida."),
  instructions: z.string().min(1, "Las instrucciones son requeridas."),
});

export const prescriptionSchema = z.object({
  patientSearch: z.string().optional(), // Campo para búsqueda de pacientes
  patientName: z.string().min(1, "El nombre del paciente es requerido."),
  idCard: z.string().optional(),
  patientAge: z.string().min(1, "La edad del paciente es requerida."),
  weight: z.string().optional(),
  height: z.string().optional(),
  diagnosis: z.string().optional(),
  generalInstructions: z.string().optional(),
  prescriptionDate: z.string().min(1, "La fecha de prescripción es requerida."),
  medications: z.array(medicationSchema).min(1, "Debe haber al menos un medicamento."),
});
