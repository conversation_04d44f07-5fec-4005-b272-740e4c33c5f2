import { NextRequest, NextResponse } from 'next/server'
import { verifyToken, hashPassword } from '@/lib/auth/auth'
import prisma from '@/lib/prisma'

export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  const token = req.cookies.get('token')?.value
  if (!token) {
    return NextResponse.json({ error: 'No autenticado' }, { status: 401 })
  }

  try {
    const user = verifyToken(token)
    if (typeof user !== 'object' || user === null || (user as any).role !== 'admin') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 403 })
    }

    const { id } = params
    const data = await req.json()
    const { password, ...userData } = data

    if (password) {
      userData.password = await hashPassword(password)
    }

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: userData,
    })

    return NextResponse.json({ message: 'Usuario actualizado', user: updatedUser })
  } catch (err) {
    console.error('Error al actualizar usuario:', err);
    return NextResponse.json({ error: 'Error interno' }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  const token = req.cookies.get('token')?.value
  if (!token) {
    return NextResponse.json({ error: 'No autenticado' }, { status: 401 })
  }

  try {
    const user = verifyToken(token)
    if (typeof user !== 'object' || user === null || (user as any).role !== 'admin') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 403 })
    }

    const { id } = params
    await prisma.user.delete({ where: { id: parseInt(id) } })
    return NextResponse.json({ message: 'Usuario eliminado' })
  } catch (err) {
    console.error('Error al eliminar usuario:', err);
    return NextResponse.json({ error: 'Error interno' }, { status: 500 })
  }
}