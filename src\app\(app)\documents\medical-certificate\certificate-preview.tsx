"use client";

import * as React from "react";
import type * as z from "zod";
import type { certificateSchema } from "./schemas";
import { GastroKidEvalLogo } from "@/components/icons";

interface CertificatePreviewProps {
  data: z.infer<typeof certificateSchema>;
}

// This component is designed to be rendered off-screen and captured for the PDF.
// The styling is self-contained and uses Tailwind CSS classes to create a professional A4 layout.
export function CertificatePreview({ data }: CertificatePreviewProps) {
  // In a real application, this data could come from a user context or global state
  const doctorInfo = {
    name: "Dr<PERSON>",
    specialty: "Gastroenterología Pediátrica",
    msp: "MSP-12345",
    email: "<EMAIL>",
    address: "Av. Principal 123, Consultorio 404",
    phone: "************",
  };

  return (
    <div
      id="pdf-content"
      className="bg-white text-black p-12 font-serif"
      style={{ width: "794px", minHeight: "1123px" }} // A4 paper dimensions in pixels (approx)
    >
      <header className="flex justify-between items-start pb-8 border-b-2 border-gray-600">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">
            {doctorInfo.name}
          </h1>
          <p className="text-lg">{doctorInfo.specialty}</p>
          <p className="text-sm text-gray-600">{doctorInfo.address}</p>
          <p className="text-sm text-gray-600">
            Tel: {doctorInfo.phone} | Email: {doctorInfo.email}
          </p>
          <p className="text-sm text-gray-600">Reg. MSP: {doctorInfo.msp}</p>
        </div>
        <div className="p-2 bg-sky-100 rounded-lg">
          <GastroKidEvalLogo className="size-16 text-sky-600" />
        </div>
      </header>

      <main className="pt-8">
        <h2 className="text-2xl font-bold text-center underline uppercase tracking-wider mb-8">
          Certificado Médico
        </h2>

        <div className="text-justify leading-relaxed space-y-4 text-base">
          <p>
            Por medio del presente, certifico que he atendido al/la paciente{" "}
            <span className="font-bold">{data.patientName || "N/A"}</span>,
            portador(a) de la cédula de identidad/pasaporte No.{" "}
            <span className="font-bold">{data.idCard || "N/A"}</span>, con
            historia clínica No.{" "}
            <span className="font-bold">{data.clinicHistoryNumber || "N/A"}</span>,
            de <span className="font-bold">{data.age || "N/A"}</span> años de
            edad.
          </p>
          <p>
            El/la paciente fue atendido/a en{" "}
            <span className="font-bold">{data.attendedAt || "N/A"}</span>, el
            día{" "}
            <span className="font-bold">
              {data.attentionDateWritten || "N/A"}
            </span>
            , correspondiente a la atención No.{" "}
            <span className="font-bold">{data.attentionNumber || "N/A"}</span>.
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <div className="space-y-2">
            <h3 className="font-bold text-lg border-b border-gray-400 pb-1">
              Diagnóstico(s)
            </h3>
            <p className="pl-4 whitespace-pre-wrap">{data.diagnosis}</p>
          </div>

          {data.procedures && (
            <div className="space-y-2">
              <h3 className="font-bold text-lg border-b border-gray-400 pb-1">
                Procedimiento(s) Realizado(s)
              </h3>
              <p className="pl-4 whitespace-pre-wrap">{data.procedures}</p>
            </div>
          )}

          {data.observations && (
            <div className="space-y-2">
              <h3 className="font-bold text-lg border-b border-gray-400 pb-1">
                Observaciones
              </h3>
              <p className="pl-4 whitespace-pre-wrap">{data.observations}</p>
            </div>
          )}
        </div>

        <div className="mt-8 pt-4 border-t border-gray-300 space-y-4">
          <h3 className="font-bold text-lg">
            Información Adicional Referida por Paciente
          </h3>
          <table className="w-full text-sm">
            <tbody>
              <tr>
                <td className="font-semibold p-1 w-1/3">Lugar de Trabajo:</td>
                <td className="p-1">{data.workplace || "N/A"}</td>
              </tr>
              <tr>
                <td className="font-semibold p-1 w-1/3">Actividad Laboral:</td>
                <td className="p-1">{data.workActivity || "N/A"}</td>
              </tr>
              <tr>
                <td className="font-semibold p-1 w-1/3">
                  Tipo de Contingencia:
                </td>
                <td className="p-1">{data.contingencyType || "N/A"}</td>
              </tr>
              <tr>
                <td className="font-semibold p-1 w-1/3">Presenta Síntomas:</td>
                <td className="p-1">{data.hasSymptoms || "N/A"}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </main>

      <footer className="mt-24 text-center absolute bottom-12 left-0 right-0">
        <div className="w-2/3 mx-auto">
          <div className="border-t-2 border-black pt-2">
            <p className="text-sm">Firma del Médico</p>
            <p className="font-bold mt-2">{doctorInfo.name}</p>
            <p className="text-sm">{doctorInfo.specialty}</p>
            <p className="text-sm">Reg. MSP: {doctorInfo.msp}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
