import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(req: Request) {
  try {
    const { phone, smsCode } = await req.json();

    if (!phone || !smsCode) {
      return NextResponse.json({ error: 'Teléfono y código SMS son requeridos' }, { status: 400 });
    }

    // Buscar usuario con el código válido y no expirado
    const user = await prisma.user.findFirst({
      where: {
        phone: {
          in: [phone, phone.replace(/^\+593/, '0')]
        },
        smsCode: smsCode,
        smsCodeExpiry: {
          gt: new Date(), // Código no expirado
        },
        active: true,
      },
      select: {
        id: true,
        phone: true,
        email: true,
        name: true,
        alias: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'Código inválido o expirado' }, { status: 400 });
    }

    // Generar token temporal para el reset de contraseña
    const crypto = require('crypto');
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hora

    // Actualizar usuario con token de reset y limpiar código SMS
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
        smsCode: null,
        smsCodeExpiry: null,
      },
    });

    return NextResponse.json({ 
      message: 'Código verificado exitosamente',
      resetToken: resetToken
    });

  } catch (error) {
    console.error('Error verificando código SMS:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}
