"use client";

import * as React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { FileDown, Loader2, PlusCircle, Trash2, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { prescriptionSchema } from "./schemas";
import { PrescriptionPreview } from "./prescription-preview";
import { useSession } from "@/hooks/use-session";
import type { SubmitHand<PERSON> } from "react-hook-form";

// Define Patient type based on Prisma schema for search results
type PatientSearchResult = {
  id: number;
  alias: string;
  name: string;
  idCard: string | null;
  gender: string | null;
  dob: string | null;
  age: string | null;
  medicalRecords?: { id: number; record_number: string }[];
  evaluations?: {
    id: number;
    weight: string | null;
    height: string | null;
    diagnosticImpression: string | null;
    createdAt: string; // Comes as string from JSON
  }[];
};

export function PrescriptionForm() {
  const { user: doctorInfo, loading: sessionLoading } = useSession();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [submittedData, setSubmittedData] = React.useState<z.infer<typeof prescriptionSchema> | null>(null);

  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchResults, setSearchResults] = React.useState<PatientSearchResult[]>([]);
  const [isSearching, setIsSearching] = React.useState(false);

  const form = useForm<z.infer<typeof prescriptionSchema>>({
    resolver: zodResolver(prescriptionSchema),
    defaultValues: {
      patientName: "",
      idCard: "",
      patientAge: "",
      weight: "",
      height: "",
      diagnosis: "",
      prescriptionDate: format(new Date(), "d 'de' MMMM 'de' yyyy", { locale: es }),
      medications: [{ name: "", presentation: "", instructions: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray<z.infer<typeof prescriptionSchema>>({
    control: form.control,
    name: "medications",
  });

  // Debounced search effect
  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (searchQuery.length > 2) {
        setIsSearching(true);
        fetch(`/api/patients/search?query=${searchQuery}`)
          .then((res) => res.json())
          .then((data: PatientSearchResult[]) => {
            setSearchResults(data);
          })
          .catch((err) => {
            console.error("Failed to search patients", err);
            toast({
              variant: "destructive",
              title: "Error de Búsqueda",
              description: "No se pudieron buscar los pacientes.",
            });
          })
          .finally(() => {
            setIsSearching(false);
          });
      } else {
        setSearchResults([]);
      }
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery, toast]); // Added toast to dependency array for consistency, though it's stable

  const handleSelectPatient = (patient: PatientSearchResult) => {
    form.setValue("patientName", patient.name);
    form.setValue("idCard", patient.idCard || "");
    form.setValue("patientAge", patient.age || "");

    // Find the latest evaluation for weight, height, and diagnosis
    if (patient.evaluations && patient.evaluations.length > 0) {
      const latestEvaluation = patient.evaluations[0]; // Already ordered by desc createdAt in API
      form.setValue("weight", latestEvaluation.weight || "");
      form.setValue("height", latestEvaluation.height || "");
      form.setValue("diagnosis", latestEvaluation.diagnosticImpression || "");
    } else {
      form.setValue("weight", "");
      form.setValue("height", "");
      form.setValue("diagnosis", "");
    }

    setSearchQuery(""); // Clear search query
    setSearchResults([]); // Clear search results
  };
  // import type { SubmitHandler } from "react-hook-form"; // Moved to top level
  import type { SubmitHandler } from "react-hook-form";

  const onSubmit: SubmitHandler<z.infer<typeof prescriptionSchema>> = (values) => {
    setSubmittedData(values);
  };

  React.useEffect(() => {
    if (submittedData) {
      setIsGenerating(true);
      toast({ title: "Generando PDF...", description: "Por favor, espere un momento." });

      const generatePdf = async () => {
        const input = document.getElementById("pdf-prescription-content");
        if (!input) {
          toast({ variant: "destructive", title: "Error", description: "No se pudo encontrar el contenido para generar el PDF." });
          setIsGenerating(false);
          setSubmittedData(null);
          return;
        }

        try {
          const canvas = await html2canvas(input, { scale: 2.5, useCORS: true });
          const imgData = canvas.toDataURL("image/png");
          const pdf = new jsPDF('l', 'mm', 'a4'); // Landscape, mm, A4
          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
          pdf.save(`receta-medica-${submittedData.patientName?.trim().replace(/\s/g, "_") || "paciente"}.pdf`);
          toast({ title: "Receta Generada", description: "El PDF se ha descargado exitosamente." });
        } catch (error) {
          console.error("Error generating PDF:", error);
          toast({ variant: "destructive", title: "Error al generar PDF", description: "Ocurrió un problema al crear el documento." });
        } finally {
          setIsGenerating(false);
          setSubmittedData(null);
        }
      };

      const timer = setTimeout(generatePdf, 500); // Increased delay
      return () => clearTimeout(timer);
    }
  }, [submittedData, toast]);

  return (
    <Form {...form}>
      <div className="absolute top-0 left-[-9999px] z-[-1] opacity-0">
        {submittedData && (
          <PrescriptionPreview
            data={submittedData}
            doctorInfo={
              doctorInfo
                ? {
                    name: doctorInfo.name ?? "",
                    specialty: doctorInfo.specialty ?? "",
                    msp: doctorInfo.msp ?? "",
                    email: doctorInfo.email ?? "",
                    phone: doctorInfo.phone ?? "",
                  }
                : null
            }
          />
        )}
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardHeader><CardTitle>Datos del Paciente</CardTitle></CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="patientSearch"
              render={({ field }) => (
                <FormItem className="md:col-span-2 relative">
                  <FormLabel>Buscar Paciente</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Buscar por nombre, cédula o historia clínica..."
                        className="pl-10"
                        {...field}
                        value={searchQuery}
                        onChange={(e) => {
                          field.onChange(e);
                          setSearchQuery(e.target.value);
                        }}
                      />
                      {isSearching && (
                        <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin" />
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                  {searchResults.length > 0 && (
                    <div className="absolute w-full bg-card border border-border rounded-md mt-1 z-10 max-h-60 overflow-y-auto shadow-lg">
                      <ul className="divide-y divide-border">
                        {searchResults.map((patient) => (
                          <li
                            key={patient.id}
                            className="px-4 py-2 hover:bg-muted cursor-pointer"
                            onClick={() => handleSelectPatient(patient)}
                          >
                            <p className="font-semibold">{patient.name}</p>
                            <p className="text-sm text-muted-foreground">
                              C.I: {patient.idCard || "N/A"} - Edad: {patient.age || "N/A"}
                            </p>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </FormItem>
              )}
            />
            <FormField control={form.control} name="patientName" render={({ field }) => (
              <FormItem><FormLabel>Nombre del Paciente</FormLabel><FormControl><Input {...field} placeholder="Ej: Juan Pérez" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
            <FormField control={form.control} name="idCard" render={({ field }) => (
              <FormItem><FormLabel>Cédula de Identidad</FormLabel><FormControl><Input {...field} placeholder="" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
            <FormField control={form.control} name="patientAge" render={({ field }) => (
              <FormItem><FormLabel>Edad del Paciente</FormLabel><FormControl><Input {...field} placeholder="Ej: 10 años" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
            <FormField control={form.control} name="weight" render={({ field }) => (
              <FormItem><FormLabel>Peso (kg)</FormLabel><FormControl><Input {...field} placeholder="Ej: 30 kg" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
            <FormField control={form.control} name="height" render={({ field }) => (
              <FormItem><FormLabel>Talla (cm)</FormLabel><FormControl><Input {...field} placeholder="Ej: 130 cm" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
            <FormField control={form.control} name="diagnosis" render={({ field }) => (
              <FormItem><FormLabel>Diagnóstico</FormLabel><FormControl><Textarea {...field} placeholder="Diagnóstico de la última evaluación" readOnly /></FormControl><FormMessage /></FormItem>
            )} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader><CardTitle>Medicamentos</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            {fields.map((field, index) => (
              <div key={field.id} className="p-4 border rounded-lg relative space-y-4">
                <Button type="button" variant="ghost" size="icon" className="absolute top-2 right-2" onClick={() => remove(index)}><Trash2 className="h-4 w-4" /></Button>
                <FormField control={form.control} name={`medications.${index}.name`} render={({ field }) => (
                  <FormItem><FormLabel>Medicamento</FormLabel><FormControl><Input {...field} placeholder="Ej: Amoxicilina 500mg" /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name={`medications.${index}.presentation`} render={({ field }) => (
                  <FormItem><FormLabel>Presentación</FormLabel><FormControl><Input {...field} placeholder="Ej: 1 tableta" /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name={`medications.${index}.instructions`} render={({ field }) => (
                  <FormItem><FormLabel>Instrucciones</FormLabel><FormControl><Textarea {...field} placeholder="Ej: Tomar cada 8 horas por 7 días." /></FormControl><FormMessage /></FormItem>
                )} />
              </div>
            ))}
            <Button type="button" variant="outline" onClick={() => append({ name: "", presentation: "", instructions: "" })}><PlusCircle className="mr-2 h-4 w-4" /> Añadir Medicamento</Button>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={isGenerating || sessionLoading}>
            {isGenerating ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Generando...</> : <><FileDown className="mr-2 h-4 w-4" />Generar y Descargar PDF</>}
          </Button>
        </div>
      </form>
    </Form>
  );
}
