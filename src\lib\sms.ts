// Servicio de SMS para recuperación de contraseña
// Nota: Este es un ejemplo básico. En producción, deberías usar un servicio como Twilio, AWS SNS, etc.

export async function sendPasswordResetSMS(phone: string, name: string, smsCode: string) {
  // En desarrollo, solo logueamos el código
  if (process.env.NODE_ENV === 'development') {
    console.log('='.repeat(50));
    console.log('📱 SMS DE RECUPERACIÓN DE CONTRASEÑA');
    console.log('='.repeat(50));
    console.log(`Para: ${phone}`);
    console.log(`Nombre: ${name}`);
    console.log(`Código de verificación: ${smsCode}`);
    console.log('='.repeat(50));
    return;
  }

  // Aquí implementarías el envío real del SMS
  // Ejemplo con diferentes servicios:

  // OPCIÓN 1: Twilio (requiere instalación: npm install twilio)
  /*
  const twilio = require('twilio');
  const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

  await client.messages.create({
    body: getSMSTemplate(name, smsCode),
    from: process.env.TWILIO_PHONE_NUMBER,
    to: phone
  });
  */

  // OPCIÓN 2: AWS SNS (requiere instalación: npm install aws-sdk)
  /*
  const AWS = require('aws-sdk');
  const sns = new AWS.SNS({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION
  });

  await sns.publish({
    Message: getSMSTemplate(name, smsCode),
    PhoneNumber: phone
  }).promise();
  */

  // OPCIÓN 3: Vonage (anteriormente Nexmo) (requiere instalación: npm install @vonage/server-sdk)
  /*
  const { Vonage } = require('@vonage/server-sdk');
  const vonage = new Vonage({
    apiKey: process.env.VONAGE_API_KEY,
    apiSecret: process.env.VONAGE_API_SECRET
  });

  await vonage.sms.send({
    to: phone,
    from: process.env.VONAGE_FROM_NUMBER,
    text: getSMSTemplate(name, smsCode)
  });
  */

  // Por ahora, simulamos el envío exitoso
  console.log(`SMS de recuperación enviado a ${phone}`);
}

function getSMSTemplate(name: string, smsCode: string): string {
  return `Hola ${name}, tu código de recuperación para GastroKid Eval es: ${smsCode}. Este código expira en 10 minutos. Si no solicitaste este código, ignora este mensaje.`;
}

// Función para generar código de 6 dígitos
export function generateSMSCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Función para validar formato de teléfono (básica)
export function isValidPhoneNumber(phone: string): boolean {
  // Acepta formatos como: +593983882267, 0983882267, 593983882267
  const phoneRegex = /^(\+?593|0)?[0-9]{9,10}$/;
  return phoneRegex.test(phone.replace(/\s+/g, ''));
}

// Función para normalizar número de teléfono
export function normalizePhoneNumber(phone: string): string {
  // Remover espacios y caracteres especiales
  let normalized = phone.replace(/[\s\-\(\)]/g, '');
  
  // Si empieza con 0, reemplazar por +593
  if (normalized.startsWith('0')) {
    normalized = '+593' + normalized.substring(1);
  }
  
  // Si no tiene código de país, agregar +593
  if (!normalized.startsWith('+')) {
    if (normalized.startsWith('593')) {
      normalized = '+' + normalized;
    } else {
      normalized = '+593' + normalized;
    }
  }
  
  return normalized;
}
