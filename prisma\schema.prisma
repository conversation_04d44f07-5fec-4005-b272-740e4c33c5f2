// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  alias     String   @unique
  password  String
  role      String
  active    Boolean
  specialty String?
  msp       String?
  phone     String?
  resetToken String?
  resetTokenExpiry DateTime?
  smsCode String?
  smsCodeExpiry DateTime?
  createdAt DateTime @default(now())
  evaluations Evaluation[] @relation("UserEvaluations")
}

model Patient {
  id         Int      @id @default(autoincrement())
  alias      String   @unique
  name       String
  idCard     String?
  gender     String?
  dob        DateTime?
  age        String?
  medicalRecords MedicalRecord[]
  parents     Parent[]
  appointments Appointment[]
  evaluations Evaluation[] @relation("PatientEvaluations")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Parent {
  id         Int    @id @default(autoincrement())
  name       String
  age        String?
  address    String?
  phone      String?
  occupation String?
  type       String  // "mother" or "father"
  patient    Patient @relation(fields: [patientId], references: [id])
  patientId  Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model MedicalRecord {
  id            Int    @id @default(autoincrement())
  record_number String @unique
  patient       Patient @relation(fields: [patientId], references: [id])
  patientId     Int
  laboratoryExams LaboratoryExam[]
}

model LaboratoryExam {
  id             Int    @id @default(autoincrement())
  alias          String @unique
  medicalRecord  MedicalRecord @relation(fields: [medicalRecordId], references: [id])
  medicalRecordId Int
}

model Appointment {
  id         Int    @id @default(autoincrement())
  date       DateTime
  time       String?
  recordNumber String?
  insurance  String?
  pediatrician String?
  referrer   String?
  patient    Patient @relation(fields: [patientId], references: [id])
  patientId  Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Evaluation {
  id         Int    @id @default(autoincrement())
  alias      String @unique
  doctor     User   @relation("UserEvaluations", fields: [doctorId], references: [id])
  doctorId   Int
  patient    Patient @relation("PatientEvaluations", fields: [patientId], references: [id])
  patientId  Int

  // Medical Information
  consultationReason String?
  currentIllness     String?
  upperDigestiveSymptoms String?
  lowerDigestiveSymptoms String?
  bowelHabits        String?
  weight             String?
  height             String?
  headCircumference  String?
  bloodPressure      String?
  temperature        String?
  cardiacFrequency   String?
  oxygenSaturation   String?

  // Patient History
  perinatalHistory       String?
  nutritionalHistory     String?
  developmentHistory     String?
  immunizations          String?
  personalMedicalHistory String?
  familyMedicalHistory   String?

  // Examination and Plan
  generalObservations    String?
  systemsReview          String?
  physicalExam           String?
  analysis               String?
  paraclinical           String?
  diagnosticImpression   String?
  actionPlan             String?

  evaluationLabExams EvaluationLabExam[]
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model EvaluationLabExam {
  id           Int    @id @default(autoincrement())
  alias        String @unique
  category     String?
  fileName     String?
  filePath     String?
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id])
  evaluationId Int
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}
