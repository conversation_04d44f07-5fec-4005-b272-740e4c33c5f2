"use client";

import * as React from "react";
import type * as z from "zod";
import type { prescriptionSchema } from "./schemas";
import { GastroKidEvalLogo } from "@/components/icons";

// Define a type for the doctor's information from the session
type DoctorInfo = {
  name: string;
  specialty: string | null;
  msp: string | null;
  email: string;
  phone: string | null;
};

interface PrescriptionPreviewProps {
  data: z.infer<typeof prescriptionSchema>;
  doctorInfo: DoctorInfo | null;
}

// Lado izquierdo: Receta completa con medicamentos
const LeftPrescriptionContent = ({ data, doctorInfo }: PrescriptionPreviewProps) => (
  <div className="border border-gray-400 p-4 h-full flex flex-col">
    {/* Header */}
    <div className="flex justify-between items-start pb-4 border-b-2 border-gray-400">
      <div className="text-left">
        <h2 className="font-bold text-lg">{doctorInfo?.name}</h2>
        <p className="text-sm">{doctorInfo?.specialty}</p>
        <p className="text-sm">Reg. MSP: {doctorInfo?.msp}</p>
        <p className="text-sm">Tel: {doctorInfo?.phone || "N/A"} | Email: {doctorInfo?.email || "N/A"}</p>
      </div>
      <GastroKidEvalLogo className="size-12 text-sky-600" />
    </div>

    {/* Body */}
    <div className="py-4 text-sm flex-grow">
      <div className="flex justify-between mb-2">
        <span><strong>Paciente:</strong> {data.patientName}</span>
        <span><strong>Identificación:</strong> {data.idCard || "N/A"}</span>
      </div>
      <div className="flex justify-between mb-2">
        <span><strong>Edad:</strong> {data.patientAge}</span>
        <span><strong>Peso:</strong> {data.weight || "N/A"}</span>
      </div>
      <div className="flex justify-between mb-2">
        <span><strong>Talla:</strong> {data.height || "N/A"}</span>
        <span><strong>Fecha:</strong> {data.prescriptionDate}</span>
      </div>

      {data.diagnosis && (
        <div className="mt-3 mb-4 text-left">
            <p className="font-bold">Diagnóstico:</p>
            <p className="pl-4 text-xs leading-tight">{data.diagnosis}</p>
        </div>
      )}

      <h3 className="text-center font-bold text-lg mb-2">Rp.</h3>

      <div className="space-y-3">
        {data.medications.map((med, index) => (
          <div key={index} className="text-left">
            <p className="font-bold">{index + 1}. {med.name}</p>
            <p className="pl-4">Presentación: {med.presentation}</p>
          </div>
        ))}
      </div>
    </div>

    {/* Footer */}
    <div className="pt-16 text-center">
        <div className="border-t-2 border-gray-600 w-2/3 mx-auto pt-1">
            <p className="text-xs">Firma y Sello</p>
        </div>
    </div>
  </div>
);

// Lado derecho: Solo indicaciones generales debajo de Rp.
const RightPrescriptionContent = ({ data, doctorInfo }: PrescriptionPreviewProps) => (
  <div className="border border-gray-400 p-4 h-full flex flex-col">
    {/* Header */}
    <div className="flex justify-between items-start pb-4 border-b-2 border-gray-400">
      <div className="text-left">
        <h2 className="font-bold text-lg">{doctorInfo?.name}</h2>
        <p className="text-sm">{doctorInfo?.specialty}</p>
        <p className="text-sm">Reg. MSP: {doctorInfo?.msp}</p>
        <p className="text-sm">Tel: {doctorInfo?.phone || "N/A"} | Email: {doctorInfo?.email || "N/A"}</p>
      </div>
      <GastroKidEvalLogo className="size-12 text-sky-600" />
    </div>

    {/* Body */}
    <div className="py-4 text-sm flex-grow">
      <div className="flex justify-between mb-2">
        <span><strong>Paciente:</strong> {data.patientName}</span>
        <span><strong>Identificación:</strong> {data.idCard || "N/A"}</span>
      </div>
      <div className="flex justify-between mb-2">
        <span><strong>Edad:</strong> {data.patientAge}</span>
        <span><strong>Peso:</strong> {data.weight || "N/A"}</span>
      </div>
      <div className="flex justify-between mb-2">
        <span><strong>Talla:</strong> {data.height || "N/A"}</span>
        <span><strong>Fecha:</strong> {data.prescriptionDate}</span>
      </div>

      {data.diagnosis && (
        <div className="mt-3 mb-4 text-left">
            <p className="font-bold">Diagnóstico:</p>
            <p className="pl-4 text-xs leading-tight">{data.diagnosis}</p>
        </div>
      )}

      <h3 className="text-center font-bold text-lg mb-2">Rp.</h3>

      {/* Indicaciones Generales - Sección principal del lado derecho */}
      <div className="text-left">
        <p className="font-bold mb-2">Indicaciones Generales:</p>
        {data.generalInstructions ? (
          <div className="pl-4 whitespace-pre-wrap text-xs leading-relaxed">
            {data.generalInstructions}
          </div>
        ) : (
          <div className="pl-4 text-xs text-gray-500 italic">
            No se han especificado indicaciones generales.
          </div>
        )}

        {/* Mostrar instrucciones específicas de cada medicamento */}
        {data.medications.some(med => med.instructions) && (
          <div className="mt-4">
            {data.medications.map((med, index) => (
              med.instructions && (
                <div key={index} className="mb-3">
                  <p className="font-semibold text-xs">{med.name}:</p>
                  <p className="pl-4 text-xs leading-relaxed">{med.instructions}</p>
                </div>
              )
            ))}
          </div>
        )}
      </div>
    </div>

    {/* Footer */}
    <div className="pt-16 text-center">
        <div className="border-t-2 border-gray-600 w-2/3 mx-auto pt-1">
            <p className="text-xs">Firma y Sello</p>
        </div>
    </div>
  </div>
);

export function PrescriptionPreview({ data, doctorInfo }: PrescriptionPreviewProps) {
  if (!doctorInfo) {
    return <div>Cargando información del médico...</div>;
  }

  return (
    <div
      id="pdf-prescription-content"
      className="bg-white text-black p-8 font-sans"
      style={{ width: "1123px", height: "794px" }} // A4 Landscape dimensions
    >
      <div className="grid grid-cols-2 gap-8 h-full">
        <LeftPrescriptionContent data={data} doctorInfo={doctorInfo} />
        <RightPrescriptionContent data={data} doctorInfo={doctorInfo} />
      </div>
    </div>
  );
}