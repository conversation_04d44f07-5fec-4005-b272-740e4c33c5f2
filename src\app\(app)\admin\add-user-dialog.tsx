
"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type * as z from "zod";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Loader2, PlusCircle } from "lucide-react";
import { createUserSchema, updateUserSchema } from "./schemas";
import { addUser, updateUser } from "./actions";
import { toast } from "@/hooks/use-toast";



import type { User } from "@prisma/client";

interface AddUserDialogProps {
  currentUser?: User;
  onOpenChange?: (open: boolean) => void;
}

export function AddUserDialog({ currentUser, onOpenChange }: AddUserDialogProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<z.infer<typeof createUserSchema | typeof updateUserSchema>>({
    resolver: zodResolver(currentUser ? updateUserSchema : createUserSchema),
    defaultValues: currentUser
      ? {
          name: currentUser.name,
          alias: currentUser.alias,
          email: currentUser.email,
          role: currentUser.role as "admin" | "medico",
          specialty: currentUser.specialty || "",
          msp: currentUser.msp || "",
          phone: currentUser.phone || "",
          password: "", // No precargar la contraseña para edición
        }
      : {
          name: "",
          alias: "",
          email: "",
          role: "medico",
          specialty: "",
          msp: "",
          phone: "",
          password: "",
        },
  });

  React.useEffect(() => {
    if (currentUser) {
      form.reset({
        name: currentUser.name,
        alias: currentUser.alias,
        email: currentUser.email,
        role: currentUser.role as "admin" | "medico",
        specialty: currentUser.specialty || "",
        msp: currentUser.msp || "",
        phone: currentUser.phone || "",
        password: "", // No precargar la contraseña para edición
      });
    } else {
      form.reset({
        name: "",
        alias: "",
        email: "",
        role: "medico",
        specialty: "",
        msp: "",
        phone: "",
        password: "",
      });
    }
  }, [currentUser, form]);

  async function onSubmit(values: z.infer<typeof createUserSchema | typeof updateUserSchema>) {
    setIsSubmitting(true);
    console.log("Submitting values:", values);
    try {
      let result;
      if (currentUser) {
        result = await updateUser(currentUser.id, values as z.infer<typeof updateUserSchema>);
      } else {
        result = await addUser(values as z.infer<typeof createUserSchema>);
      }

      if (result.success) {
        toast({
          title: "Éxito",
          description: result.message,
        });
        setIsOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
        form.reset();
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.message || "No se pudo completar la operación.",
        });
      }
    } catch (error) {
      console.error("Error submitting user form:", error);
      toast({
        variant: "destructive",
        title: "Error de servidor",
        description: "Ocurrió un error inesperado.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog
      open={isOpen || !!currentUser}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (onOpenChange) {
          onOpenChange(open);
        }
        if (!open && !currentUser) {
          form.reset();
        }
      }}
    >
      <DialogTrigger asChild>
        {!currentUser && (
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Nuevo Usuario
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{currentUser ? "Editar Usuario" : "Crear Nuevo Usuario"}</DialogTitle>
          <DialogDescription>
            {currentUser
              ? "Edite los campos del usuario." 
              : "Complete los campos para registrar un nuevo usuario en el sistema."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 py-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre Completo</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: Dr. Juan Pérez" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="alias"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alias</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: drjuan" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Correo Electrónico</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rol</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione un rol" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Administrador</SelectItem>
                        <SelectItem value="medico">Médico</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="specialty"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Especialidad</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ej: Gastroenterología"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="msp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>N° MSP</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: MSP-12345" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Teléfono</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: 0991234567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contraseña {currentUser ? "(dejar vacío para no cambiar)" : ""}</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={currentUser ? "********" : "Contraseña"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Guardando...
                  </>
                ) : currentUser ? (
                  "Guardar Cambios"
                ) : (
                  "Guardar Usuario"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
