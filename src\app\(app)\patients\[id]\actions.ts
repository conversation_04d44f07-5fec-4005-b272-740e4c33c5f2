"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth/auth";
import fs from "fs/promises";
import path from "path";

// Esquema de Zod simplificado
const newEvaluationSchema = z.object({
  consultationReason: z.string().min(1, "El motivo de consulta es requerido."),
  currentIllness: z.string().min(1, "La descripción de la enfermedad es requerida."),
  bowelHabits: z.string().optional(),
  weight: z.string().optional(),
  height: z.string().optional(),
  headCircumference: z.string().optional(),
  bloodPressure: z.string().optional(),
  temperature: z.string().optional(),
  cardiacFrequency: z.string().optional(),
  oxygenSaturation: z.string().optional(),
  generalObservations: z.string().optional(),
  systemsReview: z.string().optional(),
  physicalExam: z.string().optional(),
  analysis: z.string().optional(),
  paraclinical: z.string().optional(),
  diagnosticImpression: z.string().optional(),
  actionPlan: z.string().optional(),
});

// Función para asegurar que el directorio de subida exista
async function ensureUploadDirExists() {
  const uploadDir = path.join(process.cwd(), "public", "uploads");
  try {
    await fs.access(uploadDir);
  } catch {
    await fs.mkdir(uploadDir, { recursive: true });
  }
  return uploadDir;
}

export async function saveEvaluationForPatient(
  patientId: number,
  formData: FormData
) {
  try {
    // 1. Verificar autenticación
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) {
      return { success: false, message: "No autenticado." };
    }
    const user = verifyToken(token);
    if (typeof user !== 'object' || user === null || !(user as any).userId) {
      return { success: false, message: "Token inválido." };
    }
    const userId = (user as any).userId;

    // 2. Procesar FormData
    const jsonDataString = formData.get("jsonData") as string;
    const files = formData.getAll("files") as File[];
    const fileCategories = formData.getAll("fileCategories") as string[];

    if (!jsonDataString) {
        return { success: false, message: "No se encontraron datos del formulario." };
    }

    const validatedData = newEvaluationSchema.parse(JSON.parse(jsonDataString));

    // 3. Verificar que el paciente existe
    const patient = await prisma.patient.findUnique({ where: { id: patientId } });
    if (!patient) {
      return { success: false, message: "Paciente no encontrado." };
    }

    // 4. Procesar y guardar archivos subidos
    const uploadDir = await ensureUploadDirExists();
    const savedFilePaths: { filePath: string; fileName: string; category: string }[] = [];

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const category = fileCategories[i];
        const uniqueFileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, uniqueFileName);
        
        const buffer = Buffer.from(await file.arrayBuffer());
        await fs.writeFile(filePath, buffer);

        savedFilePaths.push({ filePath: `/uploads/${uniqueFileName}`, fileName: file.name, category });
    }

    // 5. Usar una transacción para guardar todo en la base de datos
    const evaluationAlias = `EVAL_${patient.alias}_${Date.now()}`;

    const result = await prisma.$transaction(async (tx) => {
      const evaluation = await tx.evaluation.create({
        data: {
          alias: evaluationAlias,
          doctorId: userId,
          patientId: patientId,
          ...validatedData,
        },
      });

      // Guardar referencias de archivos subidos
      if (savedFilePaths.length > 0) {
        await tx.evaluationLabExam.createMany({
            data: savedFilePaths.map(fileInfo => ({
                alias: `FILE_${evaluation.id}_${fileInfo.fileName}`,
                filePath: fileInfo.filePath,
                fileName: fileInfo.fileName,
                category: fileInfo.category,
                evaluationId: evaluation.id,
            }))
        });
      }

      return { evaluationId: evaluation.id };
    });

    // 6. Revalidar rutas y devolver éxito
    revalidatePath(`/patients/${patientId}`);
    revalidatePath("/patients");

    return {
      success: true,
      message: `Evaluación guardada exitosamente.`,
      evaluationId: result.evaluationId,
    };

  } catch (error) {
    console.error("Error saving evaluation:", error);
    if (error instanceof z.ZodError) {
      return { success: false, message: "Datos inválidos.", details: error.errors };
    }
    return { success: false, message: "Error interno del servidor." };
  }
}
