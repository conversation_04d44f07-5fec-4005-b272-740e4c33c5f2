"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { evaluationSchema } from "../new/schemas";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth/auth";

export async function updateEvaluation(
  evaluationId: number,
  data: z.infer<typeof evaluationSchema>
) {
  try {
    // Verificar autenticación
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return {
        success: false,
        message: "No autenticado. Por favor, inicie sesión.",
      };
    }

    const user = verifyToken(token);
    if (typeof user !== 'object' || user === null || !(user as any).userId) {
      return {
        success: false,
        message: "Token inválido. Por favor, inicie sesión nuevamente.",
      };
    }

    // Validar los datos
    const validatedData = evaluationSchema.parse(data);

    // Verificar que la evaluación existe
    const existingEvaluation = await prisma.evaluation.findUnique({
      where: { id: evaluationId },
      include: {
        patient: {
          include: {
            parents: true,
            medicalRecords: true,
            appointments: true
          }
        }
      }
    });

    if (!existingEvaluation) {
      return {
        success: false,
        message: "Evaluación no encontrada.",
      };
    }

    // Actualizar la evaluación en una transacción
    await prisma.$transaction(async (tx) => {
      // Actualizar información del paciente
      // await tx.patient.update({
      //   where: { id: existingEvaluation.patientId },
      //   data: {
      //     name: validatedData.patient.name,
      //     idCard: validatedData.patient.idCard,
      //     gender: validatedData.patient.gender,
      //     dob: validatedData.patient.dob,
      //     age: validatedData.patient.age,
      //   },
      // });

      // // Actualizar información de los padres
      // const mother = existingEvaluation.patient.parents?.find(p => p.type === "mother");
      // const father = existingEvaluation.patient.parents?.find(p => p.type === "father");

      // if (mother) {
      //   await tx.parent.update({
      //     where: { id: mother.id },
      //     data: {
      //       name: validatedData.mother.name,
      //       age: validatedData.mother.age || null,
      //       address: validatedData.mother.address || null,
      //       phone: validatedData.mother.phone || null,
      //       occupation: validatedData.mother.occupation || null,
      //     },
      //   });
      // } else if (validatedData.mother.name) {
      //   await tx.parent.create({
      //     data: {
      //       name: validatedData.mother.name,
      //       age: validatedData.mother.age || null,
      //       address: validatedData.mother.address || null,
      //       phone: validatedData.mother.phone || null,
      //       occupation: validatedData.mother.occupation || null,
      //       type: "mother",
      //       patientId: existingEvaluation.patientId,
      //     },
      //   });
      // }

      // if (father) {
      //   await tx.parent.update({
      //     where: { id: father.id },
      //     data: {
      //       name: validatedData.father.name,
      //       age: validatedData.father.age || null,
      //       address: validatedData.father.address || null,
      //       phone: validatedData.father.phone || null,
      //       occupation: validatedData.father.occupation || null,
      //     },
      //   });
      // } else if (validatedData.father.name) {
      //   await tx.parent.create({
      //     data: {
      //       name: validatedData.father.name,
      //       age: validatedData.father.age || null,
      //       address: validatedData.father.address || null,
      //       phone: validatedData.father.phone || null,
      //       occupation: validatedData.father.occupation || null,
      //       type: "father",
      //       patientId: existingEvaluation.patientId,
      //     },
      //   });
      // }

      // // Actualizar la cita más reciente
      // const latestAppointment = existingEvaluation.patient.appointments[0];
      // if (latestAppointment) {
      //   await tx.appointment.update({
      //     where: { id: latestAppointment.id },
      //     data: {
      //       date: validatedData.appointment.date,
      //       time: validatedData.appointment.time,
      //       insurance: validatedData.appointment.insurance,
      //       pediatrician: validatedData.appointment.pediatrician,
      //       referrer: validatedData.appointment.referrer,
      //     },
      //   });
      // }

      // // Actualizar el registro médico
      // const medicalRecord = existingEvaluation.patient.medicalRecords[0];
      // if (medicalRecord) {
      //   await tx.medicalRecord.update({
      //     where: { id: medicalRecord.id },
      //     data: {
      //       record_number: validatedData.appointment.recordNumber,
      //     },
      //   });
      // }

      // Actualizar la evaluación
      await tx.evaluation.update({
        where: { id: evaluationId },
        data: {
          // Medical Information
          consultationReason: validatedData.medical.consultationReason,
          currentIllness: validatedData.medical.currentIllness,
          upperDigestiveSymptoms: validatedData.medical.upperDigestiveSymptoms,
          lowerDigestiveSymptoms: validatedData.medical.lowerDigestiveSymptoms,
          bowelHabits: validatedData.medical.bowelHabits,
          weight: validatedData.medical.weight,
          height: validatedData.medical.height,
          headCircumference: validatedData.medical.headCircumference,
          bloodPressure: validatedData.medical.bloodPressure,
          temperature: validatedData.medical.temperature,
          cardiacFrequency: validatedData.medical.cardiacFrequency,
          oxygenSaturation: validatedData.medical.oxygenSaturation,
          
          // Patient History
          perinatalHistory: validatedData.medical.perinatalHistory,
          nutritionalHistory: validatedData.medical.nutritionalHistory,
          developmentHistory: validatedData.medical.developmentHistory,
          immunizations: validatedData.medical.immunizations,
          personalMedicalHistory: validatedData.medical.personalMedicalHistory,
          familyMedicalHistory: validatedData.medical.familyMedicalHistory,
          
          // Examination and Plan
          systemsReview: validatedData.medical.systemsReview,
          physicalExam: validatedData.medical.physicalExam,
          analysis: validatedData.medical.analysis,
          paraclinical: validatedData.medical.paraclinical,
          diagnosticImpression: validatedData.medical.diagnosticImpression,
          actionPlan: validatedData.medical.actionPlan,
        },
      });
    });

    // Revalidar las rutas relevantes
    revalidatePath(`/evaluations/${evaluationId}`);
    revalidatePath("/patients");

    console.log(`Evaluation updated with ID: ${evaluationId}`);

    return {
      success: true,
      message: "Evaluación actualizada exitosamente.",
    };
  } catch (error) {
    console.error("Error updating evaluation:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Datos de evaluación inválidos. Por favor, revise los campos requeridos.",
      };
    }

    return {
      success: false,
      message: "Error interno del servidor. Por favor, intente nuevamente.",
    };
  }
}
