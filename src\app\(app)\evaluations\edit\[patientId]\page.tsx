import { redirect, notFound } from "next/navigation";
import prisma from "@/lib/prisma";

async function getLatestEvaluationForPatient(patientId: string) {
  try {
    const patient = await prisma.patient.findUnique({
      where: { id: parseInt(patientId) },
      include: {
        evaluations: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      }
    });

    if (!patient) {
      return null;
    }

    if (patient.evaluations.length === 0) {
      return { patient, evaluation: null };
    }

    return { patient, evaluation: patient.evaluations[0] };
  } catch (error) {
    console.error("Error fetching patient evaluations:", error);
    return null;
  }
}

interface EditEvaluationRedirectPageProps {
  params: {
    patientId: string;
  };
}

export default async function EditEvaluationRedirectPage({ params }: EditEvaluationRedirectPageProps) {
  const { patientId } = await params;
  console.log("EditEvaluationRedirectPage - patientId:", patientId);

  const data = await getLatestEvaluationForPatient(patientId);
  console.log("EditEvaluationRedirectPage - data:", data);

  if (!data) {
    console.log("EditEvaluationRedirectPage - No data found, redirecting to notFound");
    notFound();
  }

  if (!data.evaluation) {
    console.log("EditEvaluationRedirectPage - No evaluation found, redirecting to new evaluation");
    // Si el paciente no tiene evaluaciones, redirigir a crear nueva evaluación
    redirect("/evaluations/new");
  }

  console.log("EditEvaluationRedirectPage - Redirecting to evaluation:", data.evaluation.id);
  // Redirigir a la página de edición de la evaluación más reciente
  redirect(`/evaluations/${data.evaluation.id}`);
}
