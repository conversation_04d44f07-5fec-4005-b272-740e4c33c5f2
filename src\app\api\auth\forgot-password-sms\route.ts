import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { sendPasswordResetSMS, generateSMSCode, isValidPhoneNumber, normalizePhoneNumber } from '@/lib/sms';

export async function POST(req: Request) {
  try {
    const { phone } = await req.json();

    if (!phone) {
      return NextResponse.json({ error: 'El número de teléfono es requerido' }, { status: 400 });
    }

    // Validar formato de teléfono
    if (!isValidPhoneNumber(phone)) {
      return NextResponse.json({ error: 'Formato de teléfono inválido' }, { status: 400 });
    }

    // Normalizar número de teléfono
    const normalizedPhone = normalizePhoneNumber(phone);

    // Buscar el usuario por teléfono
    const user = await prisma.user.findFirst({
      where: { 
        phone: {
          in: [phone, normalizedPhone, phone.replace(/^\+593/, '0')]
        },
        active: true
      },
      select: {
        id: true,
        phone: true,
        name: true,
        alias: true,
      },
    });

    // Por seguridad, siempre devolvemos el mismo mensaje, exista o no el usuario
    if (!user) {
      return NextResponse.json({ 
        message: 'Si el número de teléfono existe en nuestro sistema, recibirá un código de verificación.' 
      });
    }

    // Generar código SMS de 6 dígitos
    const smsCode = generateSMSCode();
    const smsCodeExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutos

    // Guardar el código en la base de datos
    await prisma.user.update({
      where: { id: user.id },
      data: {
        smsCode,
        smsCodeExpiry,
      },
    });

    // Enviar SMS
    try {
      await sendPasswordResetSMS(normalizedPhone, user.name || user.alias, smsCode);
    } catch (smsError) {
      console.error('Error enviando SMS:', smsError);
      // No revelamos el error del SMS por seguridad
      return NextResponse.json({ 
        message: 'Si el número de teléfono existe en nuestro sistema, recibirá un código de verificación.' 
      });
    }

    // En desarrollo, devolver el código directamente para facilitar las pruebas
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        message: 'Si el número de teléfono existe en nuestro sistema, recibirá un código de verificación.',
        smsCode: smsCode, // Solo en desarrollo
        phone: normalizedPhone
      });
    }

    return NextResponse.json({ 
      message: 'Si el número de teléfono existe en nuestro sistema, recibirá un código de verificación.' 
    });

  } catch (error) {
    console.error('Error en forgot-password-sms:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}
