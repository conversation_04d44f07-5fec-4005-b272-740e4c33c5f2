const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Hash de la contraseña
    const hashedPassword = await bcrypt.hash('123456', 10);

    // Crear usuario de prueba
    const user = await prisma.user.create({
      data: {
        name: '<PERSON> Cisne Arguello Bermeo',
        email: '<EMAIL>',
        alias: 'Dr<PERSON><PERSON> <PERSON><PERSON><PERSON>',
        password: hashedPassword,
        role: 'medico',
        active: true,
        specialty: 'Gastroenteróloga',
        msp: 'MSP-1258',
        phone: '0983882267',
      },
    });

    console.log('✅ Usuario de prueba creado exitosamente:');
    console.log(`📧 Email: ${user.email}`);
    console.log(`🔑 Contraseña: 123456`);
    console.log(`👤 Alias: ${user.alias}`);
    console.log(`🏥 Especialidad: ${user.specialty}`);

  } catch (error) {
    if (error.code === 'P2002') {
      console.log('ℹ️  El usuario ya existe en la base de datos');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Contraseña: 123456');
    } else {
      console.error('❌ Error creando usuario:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
