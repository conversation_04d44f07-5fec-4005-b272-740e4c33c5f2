
import { z } from "zod";

export const createUserSchema = z.object({
  name: z.string().min(3, { message: "El nombre debe tener al menos 3 caracteres." }),
  alias: z.string().min(3, { message: "El alias debe tener al menos 3 caracteres." }),
  email: z.string().email({ message: "Por favor ingrese un correo válido." }),
  role: z.enum(["admin", "medico"], { message: "Rol inválido." }),
  specialty: z.string().optional(),
  msp: z.string().optional(),
  phone: z.string().optional(),
  password: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres." }),
});

export const updateUserSchema = z.object({
  name: z.string().min(3, { message: "El nombre debe tener al menos 3 caracteres." }),
  alias: z.string().min(3, { message: "El alias debe tener al menos 3 caracteres." }),
  email: z.string().email({ message: "Por favor ingrese un correo válido." }),
  role: z.enum(["admin", "medico"], { message: "Rol inválido." }),
  specialty: z.string().optional(),
  msp: z.string().optional(),
  phone: z.string().optional(),
  password: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres." }).optional().or(z.literal("")),
});
