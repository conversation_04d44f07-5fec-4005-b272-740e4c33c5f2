// Servicio de email para recuperación de contraseña
// Nota: Este es un ejemplo básico. En producción, deberías usar un servicio como SendGrid, Nodemailer, etc.

export async function sendPasswordResetEmail(email: string, name: string, resetToken: string) {
  const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:9003'}/reset-password?token=${resetToken}`;
  
  // En desarrollo, solo logueamos el enlace
  if (process.env.NODE_ENV === 'development') {
    console.log('='.repeat(50));
    console.log('📧 EMAIL DE RECUPERACIÓN DE CONTRASEÑA');
    console.log('='.repeat(50));
    console.log(`Para: ${email}`);
    console.log(`Nombre: ${name}`);
    console.log(`Enlace de recuperación: ${resetUrl}`);
    console.log('='.repeat(50));
    return;
  }

  // Aquí implementarías el envío real del email
  // Ejemplo con diferentes servicios:

  // OPCIÓN 1: Nodemailer (requiere instalación: npm install nodemailer)
  /*
  const nodemailer = require('nodemailer');
  
  const transporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  await transporter.sendMail({
    from: process.env.FROM_EMAIL,
    to: email,
    subject: 'Recuperación de Contraseña - GastroKid Eval',
    html: getPasswordResetEmailTemplate(name, resetUrl),
  });
  */

  // OPCIÓN 2: SendGrid (requiere instalación: npm install @sendgrid/mail)
  /*
  const sgMail = require('@sendgrid/mail');
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);

  const msg = {
    to: email,
    from: process.env.FROM_EMAIL,
    subject: 'Recuperación de Contraseña - GastroKid Eval',
    html: getPasswordResetEmailTemplate(name, resetUrl),
  };

  await sgMail.send(msg);
  */

  // OPCIÓN 3: Resend (requiere instalación: npm install resend)
  /*
  const { Resend } = require('resend');
  const resend = new Resend(process.env.RESEND_API_KEY);

  await resend.emails.send({
    from: process.env.FROM_EMAIL,
    to: email,
    subject: 'Recuperación de Contraseña - GastroKid Eval',
    html: getPasswordResetEmailTemplate(name, resetUrl),
  });
  */

  // Por ahora, simulamos el envío exitoso
  console.log(`Email de recuperación enviado a ${email}`);
}

function getPasswordResetEmailTemplate(name: string, resetUrl: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Recuperación de Contraseña</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🔐 Recuperación de Contraseña</h1>
          <p>GastroKid Eval</p>
        </div>
        <div class="content">
          <h2>Hola ${name},</h2>
          <p>Hemos recibido una solicitud para restablecer la contraseña de tu cuenta en GastroKid Eval.</p>
          <p>Si no solicitaste este cambio, puedes ignorar este correo electrónico.</p>
          <p>Para restablecer tu contraseña, haz clic en el siguiente enlace:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Restablecer Contraseña</a>
          </p>
          <p><strong>Este enlace expirará en 1 hora por seguridad.</strong></p>
          <p>Si el botón no funciona, copia y pega el siguiente enlace en tu navegador:</p>
          <p style="word-break: break-all; background: #e9e9e9; padding: 10px; border-radius: 5px;">
            ${resetUrl}
          </p>
        </div>
        <div class="footer">
          <p>Este es un correo automático, por favor no respondas a este mensaje.</p>
          <p>&copy; ${new Date().getFullYear()} GastroKid Eval. Todos los derechos reservados.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
