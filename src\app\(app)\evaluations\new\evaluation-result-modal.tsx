
"use client";

import * as React from "react";
import type * as z from "zod";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, FileDown } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import type { evaluationSchema } from "./schemas";

interface EvaluationResultModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  data: z.infer<typeof evaluationSchema> | null;
}

export function EvaluationResultModal({
  isOpen,
  onOpenChange,
  data,
}: EvaluationResultModalProps) {
  const [isGenerating, setIsGenerating] = React.useState(false);

  const generatePdf = async () => {
    if (!data) return;

    setIsGenerating(true);
    toast({
      title: "Generando PDF...",
      description:
        "Este proceso puede tardar unos segundos. Por favor, espere.",
    });

    // Add a delay to allow the preview component and charts to render
    setTimeout(async () => {
      const input = document.getElementById("evaluation-pdf-content");
      if (!input) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "No se pudo encontrar el contenido para generar el PDF.",
        });
        setIsGenerating(false);
        return;
      }

      try {
        const canvas = await html2canvas(input, {
          scale: 2, // Higher scale for better resolution
          useCORS: true,
          windowWidth: input.scrollWidth,
          windowHeight: input.scrollHeight,
        });

        const imgData = canvas.toDataURL("image/png");
        const pdf = new jsPDF({
          orientation: "p",
          unit: "mm",
          format: "a4",
        });

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const imgProps = pdf.getImageProperties(imgData);
        const ratio = imgProps.height / imgProps.width;

        const imgWidth = pdfWidth - 20; // 10mm margin on each side
        const imgHeight = imgWidth * ratio;

        let heightLeft = imgHeight;
        let position = 10; // 10mm top margin

        // Add the first page
        pdf.addImage(imgData, "PNG", 10, position, imgWidth, imgHeight);
        heightLeft -= pdfHeight - 20;

        // Add subsequent pages if content overflows
        while (heightLeft > 0) {
          pdf.addPage();
          position = -heightLeft + 10; // Reset position for new page, sliding the image up
          pdf.addImage(imgData, "PNG", 10, position, imgWidth, imgHeight);
          heightLeft -= pdfHeight - 20;
        }

        pdf.save(
          `evaluacion-${
            data.patient.name?.trim().replace(/\s/g, "_") || "paciente"
          }.pdf`
        );

        toast({
          title: "Evaluación Guardada y Generada",
          description: "El PDF se ha descargado exitosamente.",
        });
      } catch (error) {
        console.error("Error generating PDF:", error);
        toast({
          variant: "destructive",
          title: "Error al generar PDF",
          description:
            "Ocurrió un problema al crear el documento. Intente de nuevo.",
        });
      } finally {
        setIsGenerating(false);
        onOpenChange(false); // Close modal after generation
      }
    }, 500); // 500ms delay
  };

  if (!data) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Evaluación Guardada Correctamente</DialogTitle>
          <DialogDescription>
            La evaluación para el paciente{" "}
            <strong className="text-foreground">{data.patient.name}</strong> ha
            sido registrada en el sistema.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            Ahora puedes descargar el informe completo de la evaluación en
            formato PDF.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cerrar
          </Button>
          <Button onClick={generatePdf} disabled={isGenerating}>
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando PDF...
              </>
            ) : (
              <>
                <FileDown className="mr-2 h-4 w-4" />
                Descargar Informe PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
